"""
提示词服务模块
"""
from typing import List, Optional, Dict, Any
import logging
import yaml
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class PromptService:
    """提示词服务"""

    def __init__(self):
        """初始化提示词服务"""
        self._prompts_config = None
        self._load_prompts_config()

    def _load_prompts_config(self):
        """从配置文件加载提示词"""
        try:
            # 获取配置文件路径
            config_dir = Path(__file__).parent.parent / "config"
            config_file = config_dir / "prompts.yaml"

            if not config_file.exists():
                logger.warning(f"提示词配置文件不存在: {config_file}")
                self._prompts_config = {"system_prompts": {}, "categories": []}
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                self._prompts_config = yaml.safe_load(f)

            logger.info(f"成功加载提示词配置，共 {len(self._prompts_config.get('system_prompts', {}))} 个提示词")

        except Exception as e:
            logger.error(f"加载提示词配置失败: {e}")
            # 使用默认配置
            self._prompts_config = {
                "system_prompts": {
                    "helpful": {
                        "title": "友好助手",
                        "description": "以友好、有帮助的方式回答问题",
                        "category": "基础角色",
                        "content": "你是一个友好、有帮助的AI助手。请以温和、耐心的态度回答用户的问题，提供准确和有用的信息。"
                    }
                },
                "categories": [
                    {"id": "基础角色", "name": "基础角色", "description": "定义AI助手的基本角色和回答风格"}
                ]
            }
    
    def get_available_prompts(self) -> List[Dict[str, Any]]:
        """获取可用的预设提示词列表"""
        prompts = []
        system_prompts = self._prompts_config.get('system_prompts', {})

        for prompt_id, prompt_data in system_prompts.items():
            prompts.append({
                'id': prompt_id,
                'title': prompt_data.get('title', prompt_id),
                'description': prompt_data.get('description', ''),
                'category': prompt_data.get('category', '其他')
            })
        return prompts

    def get_categories(self) -> List[Dict[str, Any]]:
        """获取提示词分类列表"""
        return self._prompts_config.get('categories', [])
    
    def get_prompt_content(self, prompt_id: str) -> Optional[str]:
        """获取指定提示词的内容"""
        system_prompts = self._prompts_config.get('system_prompts', {})
        if prompt_id in system_prompts:
            return system_prompts[prompt_id].get('content', '')
        return None
    
    def build_system_prompt(
        self, 
        selected_prompts: Optional[List[str]] = None, 
        custom_prompt: Optional[str] = None
    ) -> Optional[str]:
        """
        构建系统提示词
        
        Args:
            selected_prompts: 选中的预设提示词ID列表
            custom_prompt: 自定义提示词
            
        Returns:
            构建好的系统提示词，如果没有任何提示词则返回None
        """
        prompt_parts = []
        
        # 添加预设提示词
        if selected_prompts:
            for prompt_id in selected_prompts:
                content = self.get_prompt_content(prompt_id)
                if content:
                    prompt_parts.append(content)
                    logger.info(f"添加预设提示词: {prompt_id}")
        
        # 添加自定义提示词
        if custom_prompt and custom_prompt.strip():
            prompt_parts.append(custom_prompt.strip())
            logger.info("添加自定义提示词")
        
        if not prompt_parts:
            return None
        
        # 组合所有提示词
        if len(prompt_parts) == 1:
            system_prompt = prompt_parts[0]
        else:
            # 多个提示词时，用换行分隔并添加说明
            system_prompt = "请遵循以下指导原则：\n\n" + "\n\n".join(prompt_parts)
        
        logger.info(f"构建系统提示词完成，长度: {len(system_prompt)} 字符")
        return system_prompt
    
    def validate_prompt_ids(self, prompt_ids: List[str]) -> List[str]:
        """
        验证提示词ID列表，返回有效的ID

        Args:
            prompt_ids: 提示词ID列表

        Returns:
            有效的提示词ID列表
        """
        if not prompt_ids:
            return []

        system_prompts = self._prompts_config.get('system_prompts', {})
        valid_ids = []
        for prompt_id in prompt_ids:
            if prompt_id in system_prompts:
                valid_ids.append(prompt_id)
            else:
                logger.warning(f"无效的提示词ID: {prompt_id}")

        return valid_ids

    def reload_config(self):
        """重新加载配置文件"""
        self._load_prompts_config()
        logger.info("提示词配置已重新加载")

# 创建全局实例
prompt_service = PromptService()
