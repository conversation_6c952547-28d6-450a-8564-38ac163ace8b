# app/services/chat_service.py - 聊天服务

import json
import httpx
import logging
import re
from typing import Optional, Dict, Any, AsyncGenerator, List
from datetime import datetime

from ..core.config import settings, get_model_id
from ..services.minio_service import minio_service
from .record_api_service import record_api_service
from .prompt_service import prompt_service

logger = logging.getLogger(__name__)

class ChatService:
    """聊天服务类"""

    def __init__(self):
        self.model_api_url = settings.MODEL_API_BASE_URL
        self.record_api_url = settings.RECORD_API_BASE_URL

        # 模型能力缓存
        self._model_capabilities = {}

    async def get_model_capabilities(self, model_name: str) -> Dict[str, Any]:
        """获取模型能力信息"""
        if model_name in self._model_capabilities:
            return self._model_capabilities[model_name]

        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {settings.MODEL_API_KEY}"}
                response = await client.get(
                    f"{self.model_api_url}/api/v1/models",
                    headers=headers,
                    timeout=10.0
                )

                if response.status_code == 200:
                    models_data = response.json()
                    for model in models_data.get("data", []):
                        if model.get("id") == model_name:
                            capabilities = {
                                "supports_vision": model.get("features", {}).get("visible", False),
                                "pricing": model.get("pricing", {}),
                                "features": model.get("features", {})
                            }
                            self._model_capabilities[model_name] = capabilities
                            return capabilities

                # 如果没找到模型，默认为文本模型
                default_capabilities = {
                    "supports_vision": False,
                    "pricing": {},
                    "features": {}
                }
                self._model_capabilities[model_name] = default_capabilities
                return default_capabilities

        except Exception as e:
            logger.error(f"获取模型能力失败: {e}")
            # 默认为文本模型
            default_capabilities = {
                "supports_vision": False,
                "pricing": {},
                "features": {}
            }
            self._model_capabilities[model_name] = default_capabilities
            return default_capabilities

    def _format_message_content(self, content: str, supports_vision: bool = False) -> Any:
        """根据模型能力格式化消息内容"""
        if not supports_vision:
            # 文本模型：直接返回字符串，移除图片标记
            text_content = re.sub(r'!\[[^\]]*\]\(data:[^)]+\)', '', content).strip()
            return text_content if text_content else content

        # 可视模型：构建数组格式
        try:
            # 提取图片URL
            image_urls = re.findall(r'!\[[^\]]*\]\((data:image[^)]+)\)', content)
            # 移除图片标记，保留文本
            text_only = re.sub(r'!\[[^\]]*\]\(data:[^)]+\)', '', content).strip()

            content_parts = []
            if text_only:
                content_parts.append({"type": "text", "text": text_only})

            for url in image_urls:
                content_parts.append({
                    "type": "image_url",
                    "image_url": {"url": url}
                })

            # 如果没有内容部分，至少返回一个空文本
            if not content_parts:
                content_parts.append({"type": "text", "text": content})

            return content_parts

        except Exception as e:
            logger.error(f"格式化可视消息内容失败: {e}")
            # 降级为文本格式
            return [{"type": "text", "text": content}]

    async def get_model_capabilities(self, model_name: str) -> Dict[str, Any]:
        """获取模型能力信息"""
        if model_name in self._model_capabilities:
            return self._model_capabilities[model_name]

        try:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {settings.MODEL_API_KEY}"}
                response = await client.get(
                    f"{self.model_api_url}/api/v1/models",
                    headers=headers,
                    timeout=10.0
                )

                if response.status_code == 200:
                    models_data = response.json()
                    for model in models_data.get("data", []):
                        if model.get("id") == model_name:
                            capabilities = {
                                "supports_vision": model.get("features", {}).get("visible", False),
                                "pricing": model.get("pricing", {}),
                                "features": model.get("features", {})
                            }
                            self._model_capabilities[model_name] = capabilities
                            return capabilities

                # 如果没找到模型，默认为文本模型
                default_capabilities = {
                    "supports_vision": False,
                    "pricing": {},
                    "features": {}
                }
                self._model_capabilities[model_name] = default_capabilities
                return default_capabilities

        except Exception as e:
            logger.error(f"获取模型能力失败: {e}")
            # 默认为文本模型
            default_capabilities = {
                "supports_vision": False,
                "pricing": {},
                "features": {}
            }
            self._model_capabilities[model_name] = default_capabilities
            return default_capabilities

    def _format_message_content(self, content: str, supports_vision: bool = False) -> Any:
        """根据模型能力格式化消息内容"""
        if not supports_vision:
            # 文本模型：直接返回字符串，移除图片标记
            text_content = re.sub(r'!\[[^\]]*\]\(data:[^)]+\)', '', content).strip()
            return text_content if text_content else content

        # 可视模型：构建数组格式
        try:
            # 提取图片URL
            image_urls = re.findall(r'!\[[^\]]*\]\((data:image[^)]+)\)', content)
            # 移除图片标记，保留文本
            text_only = re.sub(r'!\[[^\]]*\]\(data:[^)]+\)', '', content).strip()

            content_parts = []
            if text_only:
                content_parts.append({"type": "text", "text": text_only})

            for url in image_urls:
                content_parts.append({
                    "type": "image_url",
                    "image_url": {"url": url}
                })

            # 如果没有内容部分，至少返回一个空文本
            if not content_parts:
                content_parts.append({"type": "text", "text": content})

            return content_parts

        except Exception as e:
            logger.error(f"格式化可视消息内容失败: {e}")
            # 降级为文本格式
            return [{"type": "text", "text": content}]

    async def create_user_message(
        self,
        api_key: str,
        conversation_id: int,
        content: str,
        model_name: str = None
    ) -> Optional[Dict[str, Any]]:
        """创建用户消息"""
        try:
            # 处理消息内容并上传到MinIO
            temp_message_id = int(datetime.now().timestamp() * 1000000)
            try:
                message_urls = await minio_service.process_message_content(
                    temp_message_id, content, role="user", conversation_id=conversation_id
                )
            except Exception as e:
                logger.warning(f"MinIO处理失败: {e}，使用占位符URL")
                message_urls = {
                    "original_url": f"https://placeholder.example.com/message/{temp_message_id}/original",
                }

            # 使用 Record API 服务创建消息
            data = {
                "conversation_id": conversation_id,
                "role": "user",
                "content_url": message_urls.get("original_url")
            }

            message_data = await record_api_service.create_message(data, api_key=api_key)
            if message_data:
                logger.info(f"用户消息创建成功: ID={message_data.get('id')}")
                return message_data
            else:
                logger.error("Record API创建消息失败")
                raise Exception("Record API创建消息失败")

        except Exception as e:
            logger.error(f"创建用户消息异常: {e}")
            raise

    async def generate_ai_response(
        self,
        api_key: str,
        conversation_id: int,
        user_message: str,
        model_name: str = None,
        temperature: float = None,
        max_tokens: int = None,
        stream: bool = False,
        selected_prompts: Optional[List[str]] = None,
        custom_prompt: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """生成AI回复"""
        try:
            print(f"🚀 开始生成AI回复，对话ID: {conversation_id}, 模型: {model_name}")

            # 获取模型能力
            model_capabilities = await self.get_model_capabilities(model_name)
            supports_vision = model_capabilities.get("supports_vision", False)

            print(f"🔍 模型 {model_name} 支持视觉: {supports_vision}")
            logger.info(f"🔍 模型 {model_name} 支持视觉: {supports_vision}")

            # 获取对话历史作为上下文
            print(f"🔍 获取对话 {conversation_id} 的历史消息作为上下文...")
            logger.info(f"🔍 获取对话 {conversation_id} 的历史消息作为上下文...")

            history = await self.get_conversation_history(api_key, conversation_id, limit=20, supports_vision=supports_vision)

            print(f"📚 获取到历史消息: {len(history) if history else 0} 条")

            # 构建系统提示词
            system_prompt = None
            if selected_prompts or custom_prompt:
                system_prompt = prompt_service.build_system_prompt(
                    selected_prompts=selected_prompts,
                    custom_prompt=custom_prompt
                )
                print(f"🎯 构建系统提示词: {system_prompt[:100] if system_prompt else 'None'}...")
                logger.info(f"🎯 系统提示词长度: {len(system_prompt) if system_prompt else 0}")

            # 构建消息列表，包含历史上下文
            messages = []

            # 如果有系统提示词，添加到消息列表开头
            if system_prompt:
                messages.append({
                    "role": "system",
                    "content": system_prompt
                })
                print(f"✅ 添加系统提示词到消息列表")

            if history:
                print(f"📚 加载了 {len(history)} 条历史消息作为上下文")
                logger.info(f"📚 加载了 {len(history)} 条历史消息作为上下文")

                # 历史消息已经通过get_conversation_history格式化过了
                for i, msg in enumerate(history):
                    role = msg.get("role")
                    content = msg.get("content")

                    print(f"📝 添加历史消息 {i}: role={role}, content_type={type(content)}")

                    if content is None:
                        print(f"⚠️ 跳过null内容的历史消息: {msg}")
                        continue

                    messages.append({
                        "role": role,
                        "content": content
                    })
            else:
                print(f"📚 没有历史消息")
                logger.info(f"📚 没有历史消息")

            # 添加当前用户消息
            formatted_user_content = self._format_message_content(user_message, supports_vision)
            messages.append({
                "role": "user",
                "content": formatted_user_content
            })

            print(f"💬 最终消息列表包含 {len(messages)} 条消息（包括当前用户消息）")
            logger.info(f"💬 最终消息列表包含 {len(messages)} 条消息（包括当前用户消息）")

            # 构建聊天请求（OpenAI格式）
            payload = {
                "model": model_name,
                "messages": messages,
                "stream": stream
            }

            # 只添加非None的可选参数
            print(f"🔧 参数检查: temperature={temperature}, max_tokens={max_tokens}")
            if temperature is not None:
                payload["temperature"] = temperature
            if max_tokens is not None:
                payload["max_tokens"] = max_tokens
                print(f"⚠️ 添加了max_tokens限制: {max_tokens}")
            else:
                print(f"✅ 没有max_tokens限制，模型可自由输出")

            # 调试：打印发送给Model API的请求
            print(f"🔍 发送给Model API的请求:")
            print(f"  - model: {payload.get('model')}")
            print(f"  - messages count: {len(payload.get('messages', []))}")
            print(f"  - stream: {payload.get('stream')}")
            print(f"  - supports_vision: {supports_vision}")
            print(f"  - max_tokens: {payload.get('max_tokens', 'NOT_SET')}")
            for i, msg in enumerate(payload.get('messages', [])):
                content = msg.get('content')
                content_type = type(content).__name__
                if isinstance(content, list):
                    content_preview = f"[{len(content)} parts]"
                else:
                    content_preview = repr(content[:100] if content else content)
                print(f"  - message {i}: role={msg.get('role')}, content_type={content_type}, content_preview={content_preview}")

                if content is None:
                    print(f"  ⚠️ 发现null内容的消息: {msg}")

            logger.info(f"🔍 发送给Model API的请求: model={model_name}, messages_count={len(messages)}, supports_vision={supports_vision}")

            async with httpx.AsyncClient() as client:
                # 使用配置的Model API密钥
                headers = {"Authorization": f"Bearer {settings.MODEL_API_KEY}"}
                timeout = httpx.Timeout(600.0)  # 增加超时时间到600秒
                response = await client.post(
                    f"{self.model_api_url}/api/v1/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=timeout
                )

                if response.status_code == 200:
                    if stream:
                        # 处理流式响应（usage 暂不可用）
                        content = await self._handle_stream_response(response)
                        return {"content": content, "usage": None}
                    else:
                        # 处理普通响应
                        data = response.json()
                        usage = data.get("usage") or data.get("token_usage")
                        if "choices" in data and len(data["choices"]) > 0:
                            content = data["choices"][0]["message"].get("content")
                            logger.info("AI回复生成成功")
                            return {"content": content, "usage": usage}
                        else:
                            logger.error("AI回复格式异常")
                            raise Exception("API回复格式异常")
                else:
                    error_text = response.text
                    logger.error(f"AI回复生成失败: HTTP {response.status_code}, 响应: {error_text}")
                    raise Exception(f"API调用失败: {response.status_code} - {error_text}")

        except httpx.TimeoutException:
            logger.error("AI回复生成超时")
            raise Exception("AI回复生成超时，请稍后重试")
        except Exception as e:
            logger.error(f"AI回复生成异常: {e}")
            raise Exception(f"AI回复生成失败: {str(e)}")

    async def create_ai_message(
        self,
        api_key: str,
        conversation_id: int,
        content: str,
        model_name: str = None,
        is_error: bool = False,
        temperature: float = None,
        cost_info: dict = None
    ) -> Optional[Dict[str, Any]]:
        """创建AI消息"""
        try:
            # 处理消息内容并上传到MinIO
            temp_message_id = int(datetime.now().timestamp() * 1000000)
            try:
                message_urls = await minio_service.process_message_content(
                    temp_message_id, content, role="assistant", conversation_id=conversation_id,
                    model_display_name=model_name, temperature=temperature, cost_info=cost_info
                )
            except Exception as e:
                logger.warning(f"MinIO处理失败: {e}，使用占位符URL")
                message_urls = {
                    "original_url": f"https://placeholder.example.com/message/{temp_message_id}/original",
                }

            # 使用 Record API 服务创建AI消息
            data = {
                "conversation_id": conversation_id,
                "role": "assistant",
                "content_url": message_urls.get("original_url")
            }

            # 添加AI消息特有的字段
            if model_name:
                model_id = get_model_id(model_name)
                data["model_id"] = model_id
            if temperature is not None:
                data["temperature"] = str(temperature)
            if is_error:
                data["is_error"] = is_error

            try:
                message_data = await record_api_service.create_message(data, api_key=api_key)
                if message_data:
                    real_message_id = message_data.get('id')
                    logger.info(f"AI消息创建成功: ID={real_message_id}")

                    # 创建费用记录（如果有费用信息）
                    if cost_info:
                        try:
                            prompt_tokens = cost_info.get('prompt_tokens', 0)
                            completion_tokens = cost_info.get('completion_tokens', 0)
                            total_cost = cost_info.get('total_cost', 0)

                            cost_data = {
                                "message_id": real_message_id,
                                "conversation_id": conversation_id,
                                "model_name": model_name or "unknown",
                                "prompt_tokens": prompt_tokens,
                                "completion_tokens": completion_tokens,
                                "total_cost": str(total_cost)
                            }
                            await record_api_service.create_message_cost(real_message_id, cost_data, api_key=api_key)
                            logger.info(f"费用记录创建成功: 消息ID={real_message_id}, 费用={total_cost}")
                        except Exception as cost_error:
                            logger.warning(f"费用记录创建失败: {cost_error}")

                    return message_data
                else:
                    logger.error("Record API创建AI消息失败")
                    raise Exception("Record API创建AI消息失败")
            except Exception as e:
                logger.error(f"创建AI消息异常: {e}")
                raise
        except Exception as e:
            logger.error(f"创建AI消息异常: {e}")
            raise

    async def _handle_stream_response(self, response: httpx.Response) -> str:
        """处理流式响应"""
        content_parts = []

        async for line in response.aiter_lines():
            if line.startswith("data: "):
                data_str = line[6:]  # 移除 "data: " 前缀

                if data_str == "[DONE]":
                    break

                try:
                    data = json.loads(data_str)
                    if "choices" in data and len(data["choices"]) > 0:
                        delta = data["choices"][0].get("delta", {})
                        if "content" in delta:
                            content_parts.append(delta["content"])
                except json.JSONDecodeError:
                    continue

        return "".join(content_parts)

    async def get_conversation_history(
        self,
        api_key: str,
        conversation_id: int,
        limit: int = 10,
        supports_vision: bool = False
    ) -> list:
        """获取对话历史（用于AI聊天的简化格式）"""
        try:
            # 使用 Record API 服务获取消息
            messages = await record_api_service.get_messages(conversation_id=conversation_id, api_key=api_key)

            if messages:
                # 转换为聊天格式
                chat_messages = []
                for msg in messages[-limit:]:  # 只取最近的消息
                    role = msg.get("role")
                    if role in ["user", "assistant"]:
                        content = await self._get_message_content_formatted(msg, supports_vision)
                        if content is not None:
                            chat_messages.append({
                                "role": role,
                                "content": content
                            })
                return chat_messages
            else:
                logger.warning("Record API 返回空消息列表")
                return []

        except Exception as e:
            logger.error(f"获取对话历史异常: {e}")
            raise

    async def get_conversation_messages(
        self,
        conversation_id: int,
        user_mathjax_setting: bool = False,
        limit: int = 50,
        auth_api_key: str | None = None,
    ) -> list:
        """获取对话消息（用于前端显示的完整格式）"""
        try:
            # 使用 Record API 服务获取消息
            messages = await record_api_service.get_messages(
                conversation_id=conversation_id,
                api_key=auth_api_key
            )

            if messages:
                # 处理消息，根据用户设置选择正确的URL
                processed_messages = []
                for msg in messages[-limit:]:  # 只取最近的消息
                    processed_msg = {
                        "id": msg.get("id"),
                        "conversation_id": msg.get("conversation_id"),
                        "role": msg.get("role"),
                        "created_at": msg.get("created_at"),
                        "updated_at": msg.get("updated_at"),
                        "model_id": msg.get("model_id"),
                        "temperature": msg.get("temperature"),
                        "max_tokens": msg.get("max_tokens"),
                        "is_error": msg.get("is_error", False),
                        "error_info": msg.get("error_info"),
                        "cost": msg.get("cost")
                    }

                    # 选择展示URL：优先plain_html_url，其次content_url
                    display_url = (
                        msg.get("original_url")
                    )
                    processed_msg["content_url"] = display_url
                    processed_msg["content_type"] = "html" if display_url == msg.get("plain_html_url") or display_url == msg.get("rendered_plain_url") else "original"

                    # 保留字段以备前端需要
                    processed_msg["content_url_raw"] = msg.get("content_url")
                    processed_msg["plain_html_url"] = msg.get("plain_html_url")
                    processed_msg["original_url"] = msg.get("original_url")
                    processed_msg["rendered_plain_url"] = msg.get("rendered_plain_url")

                    processed_messages.append(processed_msg)

                return processed_messages
            else:
                logger.warning("Record API 返回空消息列表")
                return []

        except Exception as e:
            logger.error(f"获取对话消息异常: {e}")
            raise

    async def _get_message_content(self, message: Dict[str, Any]) -> Optional[str]:
        """从消息记录中获取内容（原始字符串格式）"""
        # 兼容新字段：优先从 content_url 获取原始内容（通常是JSON）；否则尝试 plain_html_url
        original_url = message.get("content_url") or message.get("original_url")
        if not original_url or str(original_url).startswith("placeholder://"):
            return None

        try:
            content = await minio_service.download_content_from_url(original_url)
            return content
        except Exception as e:
            logger.error(f"获取消息内容异常: {e}")
            return None

    async def _get_message_content_formatted(self, message: Dict[str, Any], supports_vision: bool = False) -> Optional[Any]:
        """从消息记录中获取内容并根据模型能力格式化"""
        original_url = message.get("content_url") or message.get("original_url")
        if not original_url:
            return None

        try:
            # 使用MinIO服务的新方法来下载并格式化内容
            formatted_content = await minio_service.download_and_format_content(original_url, supports_vision)
            return formatted_content
        except Exception as e:
            logger.error(f"获取并格式化消息内容异常: {e}")
            return None

# 创建全局聊天服务实例
chat_service = ChatService()
