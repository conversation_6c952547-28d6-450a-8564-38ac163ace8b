"""
Markdown转换API
支持将Markdown转换为DOCX和PPT格式
"""

import os
import tempfile
import subprocess
import time
from pathlib import Path
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import FileResponse
from pydantic import BaseModel
import logging

from app.api.auth import get_current_user_dependency
from app.models.schemas import UserInfo

logger = logging.getLogger(__name__)

router = APIRouter()

class MarkdownConvertRequest(BaseModel):
    markdown: str
    filename: Optional[str] = None

def check_pandoc_installed():
    """检查pandoc是否已安装"""
    try:
        result = subprocess.run(['pandoc', '--version'], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def convert_markdown_with_pandoc(markdown_content: str, output_format: str, 
                                output_file: str) -> bool:
    """使用pandoc转换Markdown"""
    try:
        # 创建临时Markdown文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', 
                                       delete=False, encoding='utf-8') as temp_md:
            temp_md.write(markdown_content)
            temp_md_path = temp_md.name
        
        try:
            # 构建pandoc命令
            cmd = ['pandoc', temp_md_path, '-o', output_file]
            
            # 根据输出格式添加特定参数
            if output_format == 'docx':
                cmd.extend(['--from', 'markdown', '--to', 'docx'])
                reference_doc = get_reference_doc_path('docx')
                if reference_doc:
                    cmd.extend(['--reference-doc', reference_doc])
            elif output_format == 'pptx':
                cmd.extend(['--from', 'markdown', '--to', 'pptx'])
                reference_doc = get_reference_doc_path('pptx')
                if reference_doc:
                    cmd.extend(['--reference-doc', reference_doc])
            
            logger.info(f"执行pandoc命令: {' '.join(cmd)}")
            
            # 执行转换
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                logger.error(f"Pandoc转换失败: {result.stderr}")
                return False
                
            return True
            
        finally:
            # 清理临时Markdown文件
            try:
                os.unlink(temp_md_path)
            except OSError:
                pass
                
    except Exception as e:
        logger.error(f"转换过程中出现异常: {e}")
        return False

def get_reference_doc_path(format_type: str) -> str:
    """获取参考文档路径"""
    # 这里可以放置自定义的模板文件
    # 如果没有自定义模板，pandoc会使用默认模板
    templates_dir = Path(__file__).parent.parent / 'templates'
    
    if format_type == 'docx':
        template_path = templates_dir / 'reference.docx'
    elif format_type == 'pptx':
        template_path = templates_dir / 'reference.pptx'
    else:
        return ''
    
    # 如果模板文件存在，返回路径；否则返回空字符串使用默认模板
    return str(template_path) if template_path.exists() else ''

@router.post("/markdown-to-docx")
async def convert_markdown_to_docx(
    request: MarkdownConvertRequest,
    current_user: UserInfo = Depends(get_current_user_dependency)
):
    """将Markdown转换为DOCX文档"""
    
    # 检查pandoc是否可用
    if not check_pandoc_installed():
        raise HTTPException(
            status_code=500,
            detail="服务器未安装pandoc，无法进行文档转换"
        )
    
    # 验证输入
    if not request.markdown.strip():
        raise HTTPException(
            status_code=400,
            detail="Markdown内容不能为空"
        )
    
    # 生成文件名
    filename = request.filename or f"document_{current_user.id}_{int(time.time())}.docx"
    if not filename.endswith('.docx'):
        filename += '.docx'
    
    # 创建临时输出文件
    with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_output:
        output_path = temp_output.name
    
    try:
        # 执行转换
        success = convert_markdown_with_pandoc(
            request.markdown, 'docx', output_path
        )
        
        if not success:
            raise HTTPException(
                status_code=500,
                detail="文档转换失败，请检查Markdown格式"
            )
        
        # 检查输出文件是否存在
        if not os.path.exists(output_path):
            raise HTTPException(
                status_code=500,
                detail="转换完成但未生成输出文件"
            )
        
        logger.info(f"成功转换Markdown为DOCX: {filename}")
        
        # 返回文件
        return FileResponse(
            path=output_path,
            filename=filename,
            media_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            background=lambda: os.unlink(output_path)  # 下载完成后删除临时文件
        )
        
    except HTTPException:
        # 清理临时文件
        try:
            os.unlink(output_path)
        except OSError:
            pass
        raise
    except Exception as e:
        # 清理临时文件
        try:
            os.unlink(output_path)
        except OSError:
            pass
        logger.error(f"转换DOCX时出现异常: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"转换过程中出现错误: {str(e)}"
        )

@router.post("/markdown-to-ppt")
async def convert_markdown_to_ppt(
    request: MarkdownConvertRequest,
    current_user: UserInfo = Depends(get_current_user_dependency)
):
    """将Markdown转换为PPT演示文稿"""
    
    # 检查pandoc是否可用
    if not check_pandoc_installed():
        raise HTTPException(
            status_code=500,
            detail="服务器未安装pandoc，无法进行文档转换"
        )
    
    # 验证输入
    if not request.markdown.strip():
        raise HTTPException(
            status_code=400,
            detail="Markdown内容不能为空"
        )
    
    # 生成文件名
    filename = request.filename or f"presentation_{current_user.id}_{int(time.time())}.pptx"
    if not filename.endswith('.pptx'):
        filename += '.pptx'
    
    # 创建临时输出文件
    with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as temp_output:
        output_path = temp_output.name
    
    try:
        # 执行转换
        success = convert_markdown_with_pandoc(
            request.markdown, 'pptx', output_path
        )
        
        if not success:
            raise HTTPException(
                status_code=500,
                detail="演示文稿转换失败，请检查Markdown格式"
            )
        
        # 检查输出文件是否存在
        if not os.path.exists(output_path):
            raise HTTPException(
                status_code=500,
                detail="转换完成但未生成输出文件"
            )
        
        logger.info(f"成功转换Markdown为PPT: {filename}")
        
        # 返回文件
        return FileResponse(
            path=output_path,
            filename=filename,
            media_type='application/vnd.openxmlformats-officedocument.presentationml.presentation',
            background=lambda: os.unlink(output_path)  # 下载完成后删除临时文件
        )
        
    except HTTPException:
        # 清理临时文件
        try:
            os.unlink(output_path)
        except OSError:
            pass
        raise
    except Exception as e:
        # 清理临时文件
        try:
            os.unlink(output_path)
        except OSError:
            pass
        logger.error(f"转换PPT时出现异常: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"转换过程中出现错误: {str(e)}"
        )

@router.get("/pandoc-status")
async def get_pandoc_status(current_user: UserInfo = Depends(get_current_user_dependency)):
    """获取pandoc安装状态"""
    is_installed = check_pandoc_installed()
    
    if is_installed:
        try:
            result = subprocess.run(['pandoc', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            version_info = result.stdout.split('\n')[0] if result.stdout else "未知版本"
        except:
            version_info = "版本信息获取失败"
    else:
        version_info = None
    
    return {
        "installed": is_installed,
        "version": version_info,
        "supported_formats": ["docx", "pptx"] if is_installed else []
    }
