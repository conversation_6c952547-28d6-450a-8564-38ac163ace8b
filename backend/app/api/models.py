# app/api/models.py - 模型API路由

import logging
import httpx
from fastapi import APIRouter, HTTPException, status, Depends
from typing import List

from ..models.schemas import ModelsResponse, ModelInfo
from ..api.auth import get_current_user_dependency
from ..core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=ModelsResponse)
async def get_models(current_user = Depends(get_current_user_dependency)):
    """
    获取可用模型列表
    """
    try:
        async with httpx.AsyncClient() as client:
            # 使用配置的Model API密钥进行认证
            headers = {"Authorization": f"Bearer {settings.MODEL_API_KEY}"}
            response = await client.get(
                f"{settings.MODEL_API_BASE_URL}/api/v1/models",
                headers=headers,
                timeout=600.0
            )
            
            if response.status_code == 200:
                data = response.json()

                # 处理不同的响应格式
                models_data = []
                if "data" in data:
                    models_data = data["data"]
                elif "models" in data:
                    models_data = data["models"]
                elif isinstance(data, list):
                    models_data = data
                else:
                    logger.warning(f"未知的模型API响应格式: {data}")
                    models_data = []

                # 检查是否获得了真实模型
                if not models_data:
                    logger.warning("Model API返回空列表，请检查API密钥权限")
                    raise HTTPException(
                        status_code=503,
                        detail="No models available from Model API"
                    )

                # 转换为标准格式（尽量解析出“每token”价格）
                def _as_float(x):
                    try:
                        if isinstance(x, str):
                            s = x.strip().lower().replace('$', '').replace('/1k', '').replace('/k', '').replace('/1000', '')
                            return float(s)
                        return float(x)
                    except Exception:
                        return None

                def _per_token_from(model_dict: dict, which: str) -> float | None:
                    # which in {'input','output'}
                    p = model_dict.get('pricing') or {}
                    unit = (p.get('unit') or '').lower()  # e.g., 'per_1k_tokens'

                    # 优先明确的“每token”键
                    for key in [
                        f'{which}_cost_per_token', f'{which}_token', which, f'{which}_per_token',
                        f'{which}_token_price'  # 新增：上游返回 *_token_price
                    ]:
                        v = p.get(key)
                        if v is None:
                            v = model_dict.get(key)
                        fv = _as_float(v)
                        if fv is not None:
                            # 根据单位转换为每token价格
                            if 'per_1m' in unit or 'per_1000k' in unit:
                                # 每百万token -> 每token
                                return fv / 1000000.0
                            elif 'per_1k' in unit:
                                # 每千token -> 每token
                                return fv / 1000.0
                            return fv

                    # 再尝试“每千token”的键，做 /1000 换算
                    for key in [
                        f'{which}_cost_per_1k_tokens', f'{which}_per_1k', f'{which}_per1000', f'{which}_per_1000'
                    ]:
                        v = p.get(key)
                        if v is None:
                            v = model_dict.get(key)
                        fv = _as_float(v)
                        if fv is not None:
                            return fv / 1000.0

                    # 一些提供方用 prompt/completion 命名
                    alias = 'prompt' if which == 'input' else 'completion'
                    v = p.get(alias)
                    fv = _as_float(v)
                    if fv is not None:
                        # 根据单位转换为每token价格
                        if 'per_1m' in unit or 'per_1000k' in unit:
                            # 每百万token -> 每token
                            return fv / 1000000.0
                        elif 'per_1k' in unit:
                            # 每千token -> 每token
                            return fv / 1000.0
                        return fv
                    # prompt_per_1k / completion_per_1k
                    v = p.get(f'{alias}_per_1k') or p.get(f'{alias}_per1000')
                    fv = _as_float(v)
                    if fv is not None:
                        return fv / 1000.0
                    return None

                models = []
                for model in models_data:
                    if isinstance(model, dict):
                        in_pt = _per_token_from(model, 'input')
                        out_pt = _per_token_from(model, 'output')
                        if in_pt is None and out_pt is None:
                            logger.warning(f"无法解析模型单价: id={model.get('id')} name={model.get('name')} pricing={model.get('pricing')}")
                        model_info = ModelInfo(
                            id=model.get('id', ''),
                            name=model.get('name', model.get('id', '')),
                            display_name=model.get('display_name', model.get('name', model.get('id', ''))),
                            description=model.get('description'),
                            max_tokens=model.get('max_tokens'),
                            supports_images=model.get('supports_images', False),
                            input_cost_per_token=in_pt,
                            output_cost_per_token=out_pt
                        )
                        models.append(model_info)

                logger.info(f"获取到 {len(models)} 个模型")
                return ModelsResponse(
                    models=models,
                    message=f"成功获取 {len(models)} 个模型"
                )
            else:
                logger.error(f"模型API请求失败: HTTP {response.status_code}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="模型服务暂时不可用"
                )
                
    except httpx.TimeoutException:
        logger.error("模型API请求超时")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="模型服务请求超时"
        )
    except Exception as e:
        logger.error(f"获取模型列表异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型列表服务内部错误"
        )

@router.get("/{model_id}")
async def get_model_info(
    model_id: str,
    current_user = Depends(get_current_user_dependency)
):
    """
    获取特定模型信息
    """
    try:
        # 首先获取所有模型
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {settings.MODEL_API_KEY}"}
            response = await client.get(
                f"{settings.MODEL_API_BASE_URL}/api/v1/models",
                headers=headers,
                timeout=10.0
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # 处理不同的响应格式
                models_data = []
                if "data" in data:
                    models_data = data["data"]
                elif "models" in data:
                    models_data = data["models"]
                elif isinstance(data, list):
                    models_data = data
                
                # 查找指定模型
                for model in models_data:
                    if isinstance(model, dict) and model.get('id') == model_id:
                        model_info = ModelInfo(
                            id=model.get('id', ''),
                            name=model.get('name', model.get('id', '')),
                            display_name=model.get('display_name', model.get('name', model.get('id', ''))),
                            description=model.get('description'),
                            max_tokens=model.get('max_tokens'),
                            supports_images=model.get('supports_images', False)
                        )
                        return {
                            "success": True,
                            "model": model_info,
                            "message": "模型信息获取成功"
                        }
                
                # 模型未找到
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"模型 {model_id} 不存在"
                )
            else:
                logger.error(f"模型API请求失败: HTTP {response.status_code}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="模型服务暂时不可用"
                )
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型信息异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型信息服务内部错误"
        )
