<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌套代码围栏问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .code-display {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .result {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
        }
        .error {
            background: #ffe6e6;
            color: #d00;
        }
        .success {
            background: #e6ffe6;
            color: #080;
        }
    </style>
</head>
<body>
    <h1>嵌套代码围栏问题深度调试</h1>
    
    <div class="debug-section">
        <h2>问题分析</h2>
        <p>当前的处理流程：</p>
        <ol>
            <li><strong>parseNestedCodeFences</strong> - 解析嵌套围栏，用占位符替换</li>
            <li><strong>marked()</strong> - Markdown解析器处理内容</li>
            <li><strong>恢复占位符</strong> - 将占位符替换为 &lt;pre&gt;&lt;code&gt; 结构</li>
            <li><strong>processMessageContentWithCodeBlocks</strong> - 添加增强样式</li>
        </ol>
        <p><strong>问题：</strong>在步骤2中，marked()可能会对占位符进行额外处理，导致嵌套结构丢失。</p>
    </div>

    <div class="debug-section">
        <h2>测试用例：Markdown中嵌套Python</h2>
        <div class="step">
            <h4>原始输入：</h4>
            <div class="code-display" id="original-input">```markdown
这是一个包含代码的文档：
```python
def hello():
    print("Hello World")
```
文档结束
```</div>
        </div>
        
        <div class="step">
            <h4>步骤1：parseNestedCodeFences 结果</h4>
            <div class="result" id="step1-result"></div>
        </div>
        
        <div class="step">
            <h4>步骤2：marked() 处理结果</h4>
            <div class="result" id="step2-result"></div>
        </div>
        
        <div class="step">
            <h4>步骤3：恢复占位符结果</h4>
            <div class="result" id="step3-result"></div>
        </div>
        
        <div class="step">
            <h4>步骤4：最终增强结果</h4>
            <div class="result" id="step4-result"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        // 复制解析函数
        const parseNestedCodeFences = (content) => {
            const codeFencePlaceholders = []
            const lines = content.split('\n')
            const result = []

            console.log('=== parseNestedCodeFences 开始 ===')
            console.log('输入内容:', content)

            let i = 0
            while (i < lines.length) {
                const line = lines[i]
                const fenceMatch = line.match(/^(\s*)(```+)(\w*)(.*)$/)

                if (fenceMatch) {
                    const [, indent, fence, language] = fenceMatch
                    const fenceLength = fence.length

                    console.log(`找到开始围栏: 行${i+1}, 长度=${fenceLength}, 语言=${language}`)

                    // 找到开始围栏，现在寻找匹配的结束围栏
                    const blockContent = []
                    let j = i + 1
                    let found = false

                    while (j < lines.length) {
                        const currentLine = lines[j]
                        const endFenceMatch = currentLine.match(/^(\s*)(```+)(.*)$/)

                        if (endFenceMatch) {
                            const [, endIndent, endFence] = endFenceMatch
                            const endFenceLength = endFence.length

                            // 检查是否是匹配的结束围栏
                            if (endFenceLength >= fenceLength && endIndent === indent) {
                                console.log(`找到匹配的结束围栏: 行${j+1}`)
                                found = true
                                break
                            } else {
                                console.log(`不匹配的围栏: 行${j+1}, 长度=${endFenceLength}, 需要>=${fenceLength}`)
                            }
                        }

                        blockContent.push(currentLine)
                        j++
                    }

                    if (found) {
                        const index = codeFencePlaceholders.length
                        const placeholder = `CODEFENCEPLACEHOLDER${index}CODEFENCEPLACEHOLDER`
                        codeFencePlaceholders.push({
                            placeholder,
                            language: language || '',
                            content: blockContent.join('\n'),
                            indent
                        })
                        result.push(placeholder)
                        console.log(`保存代码块${index}: 语言=${language}, 内容="${blockContent.join('\\n')}"`)
                        i = j + 1
                    } else {
                        result.push(line)
                        i++
                    }
                } else {
                    result.push(line)
                    i++
                }
            }

            const finalResult = {
                content: result.join('\n'),
                placeholders: codeFencePlaceholders
            }
            
            console.log('=== parseNestedCodeFences 结果 ===')
            console.log('处理后内容:', finalResult.content)
            console.log('占位符:', finalResult.placeholders)
            
            return finalResult
        }

        // 模拟processMessageContentWithCodeBlocks
        const processMessageContentWithCodeBlocks = (htmlContent) => {
            console.log('=== processMessageContentWithCodeBlocks 开始 ===')
            console.log('输入HTML:', htmlContent)
            
            let processedContent = htmlContent

            processedContent = processedContent.replace(
                /<pre><code([^>]*)>([\s\S]*?)<\/code><\/pre>/g, 
                (match, attributes, codeContent) => {
                    console.log('找到代码块:', { attributes, codeContent })
                    
                    const languageMatch = attributes.match(/class="language-([a-zA-Z0-9_+\-#]+)"/)
                    const language = languageMatch ? languageMatch[1] : ''
                    
                    return `<div class="enhanced-code-block">
                        <div class="code-header">
                            ${language ? `<span class="code-language">${language.toUpperCase()}</span>` : ''}
                        </div>
                        <div class="code-content">
                            <pre><code class="hljs ${language ? `language-${language}` : ''}">${codeContent}</code></pre>
                        </div>
                    </div>`
                }
            )
            
            console.log('=== processMessageContentWithCodeBlocks 结果 ===')
            console.log('输出HTML:', processedContent)
            
            return processedContent
        }

        // 运行完整测试
        function runFullTest() {
            const originalInput = document.getElementById('original-input').textContent
            
            // 步骤1：parseNestedCodeFences
            const step1Result = parseNestedCodeFences(originalInput)
            document.getElementById('step1-result').innerHTML = `
                <strong>处理后内容：</strong><br>
                <div class="code-display">${step1Result.content}</div>
                <strong>占位符 (${step1Result.placeholders.length}个)：</strong><br>
                ${step1Result.placeholders.map((p, i) => `
                    <div style="margin: 5px 0; padding: 5px; background: #fff; border: 1px solid #ccc;">
                        <strong>块 ${i+1}:</strong> 语言=${p.language || '无'}<br>
                        <strong>内容:</strong><br>
                        <div class="code-display">${p.content}</div>
                    </div>
                `).join('')}
            `
            
            // 步骤2：marked处理
            let step2Result
            try {
                step2Result = marked.parse(step1Result.content)
                document.getElementById('step2-result').innerHTML = `
                    <div class="code-display success">${step2Result}</div>
                `
            } catch (error) {
                step2Result = step1Result.content
                document.getElementById('step2-result').innerHTML = `
                    <div class="code-display error">Marked处理失败: ${error.message}</div>
                `
            }
            
            // 步骤3：恢复占位符
            let step3Result = step2Result
            step1Result.placeholders.forEach((item) => {
                const escapedContent = item.content
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#39;')

                const languageClass = item.language ? ` class="language-${item.language}"` : ''
                const codeBlock = `<pre><code${languageClass}>${escapedContent}</code></pre>`
                
                step3Result = step3Result.split(item.placeholder).join(codeBlock)
            })
            
            document.getElementById('step3-result').innerHTML = `
                <div class="code-display">${step3Result}</div>
            `
            
            // 步骤4：增强处理
            const step4Result = processMessageContentWithCodeBlocks(step3Result)
            document.getElementById('step4-result').innerHTML = `
                <div class="code-display">${step4Result}</div>
                <strong>渲染效果：</strong><br>
                <div style="border: 1px solid #ddd; padding: 10px; margin-top: 10px;">${step4Result}</div>
            `
        }

        // 页面加载后运行测试
        window.onload = function() {
            // 确保marked已加载
            if (typeof marked !== 'undefined') {
                runFullTest()
            } else {
                setTimeout(runFullTest, 1000)
            }
        }
    </script>
</body>
</html>
