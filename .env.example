# InspirFlow 项目环境配置模板
# 复制此文件为 .env 并填入实际配置值

# ===== 应用基础配置 =====
APP_NAME=InspirFlow API
APP_VERSION=2.0.0
ENVIRONMENT=production
DEBUG=false

# ===== 网络配置 =====
# 后端配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=20010

# 前端配置  
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=20020

# ===== 安全配置 =====
# JWT 密钥 - 请生成强随机密钥
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24

# ===== 外部API配置 =====
# Model API 配置
MODEL_API_BASE_URL=http://your-model-api-server:20001
MODEL_API_KEY=your-model-api-key-here

# Record API 配置
RECORD_API_BASE_URL=http://your-record-api-server:20002
RECORD_API_KEY=your-record-api-key-here

# ===== MinIO 对象存储配置 =====
MINIO_ENDPOINT=your-minio-server:7020
MINIO_ACCESS_KEY=your-minio-access-key
MINIO_SECRET_KEY=your-minio-secret-key
MINIO_BUCKET=inspirflow-content
MINIO_SECURE=false

# ===== CORS 配置 =====
ALLOWED_ORIGINS=http://localhost:20020,http://localhost:5173,http://127.0.0.1:20020

# ===== 聊天配置 =====
DEFAULT_MODEL=gpt-4.1
DEFAULT_TEMPERATURE=0.8
MAX_TOKENS=4000

# ===== 文件上传配置 =====
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# ===== 日志配置 =====
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ===== 前端环境变量 =====
# API配置
VITE_API_BASE_URL=
VITE_PROXY_TARGET=http://127.0.0.1:20010
VITE_WS_URL=/ws

# 功能开关
VITE_DEV_MODE=false
VITE_DEBUG_LOG=false
VITE_ENABLE_LATEX=true
VITE_ENABLE_DARK_THEME=true

# UI配置
VITE_DEFAULT_THEME=light
VITE_DEFAULT_LANGUAGE=zh-CN
VITE_SIDEBAR_COLLAPSED=false

# 聊天配置
VITE_DEFAULT_MODEL=gpt-3.5-turbo
VITE_DEFAULT_TEMPERATURE=0.8
VITE_MAX_MESSAGE_LENGTH=4000
VITE_MESSAGE_HISTORY_LIMIT=50

# 文件上传配置
VITE_MAX_FILE_SIZE=10
VITE_ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp

# 性能配置
VITE_REQUEST_TIMEOUT=600000
VITE_AUTO_SAVE_INTERVAL=5000
