# InspirFlow Vue.js 前端环境配置示例
# 复制此文件为 .env 并修改相应配置

# ===== 应用配置 =====
VITE_APP_NAME=InspirFlow
VITE_APP_VERSION=2.0.0

# ===== API配置 =====
# 后端API基础URL
VITE_API_BASE_URL=http://localhost:8000/api/v1

# WebSocket URL (用于实时聊天)
VITE_WS_URL=ws://localhost:8000/ws

# ===== 功能开关 =====
# 是否启用开发模式
VITE_DEV_MODE=true

# 是否启用调试日志
VITE_DEBUG_LOG=true

# 是否启用LaTeX渲染
VITE_ENABLE_LATEX=true

# 是否启用暗色主题
VITE_ENABLE_DARK_THEME=true

# ===== UI配置 =====
# 默认主题 (light/dark/auto)
VITE_DEFAULT_THEME=light

# 默认语言 (zh-CN/en-US)
VITE_DEFAULT_LANGUAGE=zh-CN

# 侧边栏默认状态 (true/false)
VITE_SIDEBAR_COLLAPSED=false

# ===== 聊天配置 =====
# 默认AI模型
VITE_DEFAULT_MODEL=gpt-3.5-turbo

# 默认温度设置
VITE_DEFAULT_TEMPERATURE=0.8

# 最大消息长度
VITE_MAX_MESSAGE_LENGTH=4000

# 消息历史显示数量
VITE_MESSAGE_HISTORY_LIMIT=50

# ===== 文件上传配置 =====
# 最大文件大小 (MB)
VITE_MAX_FILE_SIZE=10

# 支持的图片格式
VITE_ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp

# ===== 性能配置 =====
# API请求超时时间 (毫秒)
VITE_REQUEST_TIMEOUT=600000

# 自动保存间隔 (毫秒)
VITE_AUTO_SAVE_INTERVAL=5000

# ===== 第三方服务 =====
# 错误监控服务 (如Sentry)
# VITE_SENTRY_DSN=

# 分析服务 (如Google Analytics)
# VITE_GA_TRACKING_ID=
