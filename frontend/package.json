{"name": "inspirflow-frontend", "version": "2.0.0", "description": "InspirFlow 前端应用 - Vue.js 版本", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@jupyterlite/pyodide-kernel": "^0.6.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "highlight.js": "^11.9.0", "katex": "^0.16.22", "lodash-es": "^4.17.21", "marked": "^9.1.6", "pdfjs-dist": "^3.11.174", "pinia": "^2.1.7", "pyodide": "^0.28.2", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/katex": "^0.16.7", "@types/marked": "^6.0.0", "@vitejs/plugin-vue": "^4.5.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}