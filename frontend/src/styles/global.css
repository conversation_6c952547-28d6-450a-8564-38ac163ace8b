/* 全局样式文件 */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #409eff;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;

  /* 辅助色 */
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;

  /* 文字颜色 */
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;

  /* 边框颜色 */
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;

  /* 背景颜色 */
  --bg-color: #ffffff;
  --bg-color-page: #f2f3f5;
  --bg-color-overlay: rgba(255, 255, 255, 0.9);

  /* 阴影 */
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  /* 圆角 */
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-round: 20px;
  --border-radius-circle: 100%;

  /* 字体 */
  --font-size-extra-large: 24px;
  --font-size-large: 22px;
  --font-size-medium: 20px;
  --font-size-base: 18px;
  --font-size-small: 16px;
  --font-size-extra-small: 14px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 动画 */
  --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  --transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  --transition-md-fade: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}
  /* 布局变量 */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 56px;


/* 暗色主题变量 */
.dark {
  --text-primary: #e5eaf3;
  --text-regular: #cfd3dc;
  --text-secondary: #a3a6ad;
  --text-placeholder: #8d9095;

  --border-base: #4c4d4f;
  --border-light: #414243;
  --border-lighter: #363637;
  --border-extra-light: #2b2b2c;

  --bg-color: #1d1e1f;
  --bg-color-page: #0a0a0a;
  --bg-color-overlay: rgba(29, 30, 31, 0.9);
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 18px;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-color-page);
  transition: var(--transition-base);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-base);
}

a:hover {
  color: var(--primary-light);
}

/* 按钮基础样式 */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  transition: var(--transition-base);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 输入框基础样式 */
input, textarea {
  font-family: inherit;
  outline: none;
  transition: var(--transition-base);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: var(--border-radius-base);
  transition: var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--text-primary);
}

.text-regular {
  color: var(--text-regular);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-placeholder {
  color: var(--text-placeholder);
}

.bg-primary {
  background-color: var(--primary-color);
}

.bg-white {
  background-color: var(--bg-color);
}

.border-radius {
  border-radius: var(--border-radius-base);
}

.shadow {
  box-shadow: var(--box-shadow-base);
}

.shadow-light {
  box-shadow: var(--box-shadow-light);
}

.transition {
  transition: var(--transition-base);
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* 响应式工具类 */
.hidden {
  display: none;
}

@media (max-width: 768px) {
  .hidden-mobile {
    display: none;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none;
  }
}

/* 动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: var(--transition-fade);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: var(--transition-md-fade);
}

.slide-fade-leave-active {
  transition: var(--transition-md-fade);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

/* 自定义组件样式 */
.card {
  background: var(--bg-color);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-base);
  padding: var(--spacing-md);
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--box-shadow-light);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  transition: var(--transition-base);
  cursor: pointer;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-light);
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-regular);
  border-color: var(--border-base);
}

.btn-secondary:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

/* 增强代码块样式 */
.enhanced-code-block {
  position: relative;
  margin: 16px 0;
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f1f3f4;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
}

.code-language {
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 代码块操作按钮容器 */
.code-actions {
  display: flex;
  gap: 6px;
}

/* 代码运行按钮 */
.code-run-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  color: #ffffff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Python运行按钮 - 绿色 */
.code-run-btn.python {
  background: #10b981;
  border: 1px solid #059669;
}

.code-run-btn.python:hover {
  background: #059669;
  border-color: #047857;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.code-run-btn.python:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);
}

/* HTML运行按钮 - 橙色 */
.code-run-btn.html {
  background: #f97316;
  border: 1px solid #ea580c;
}

.code-run-btn.html:hover {
  background: #ea580c;
  border-color: #c2410c;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(249, 115, 22, 0.3);
}

.code-run-btn.html:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(249, 115, 22, 0.2);
}

.code-run-btn:disabled {
  background: #9ca3af;
  border-color: #6b7280;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.code-run-btn svg {
  width: 14px;
  height: 14px;
}

/* 代码复制按钮 */
.code-copy-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.code-copy-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.code-copy-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.code-copy-btn svg {
  width: 14px;
  height: 14px;
}

/* 代码转换按钮 */
.code-convert-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  color: #ffffff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* DOCX转换按钮 - 蓝色 */
.code-convert-btn.docx {
  background: #3b82f6;
  border: 1px solid #2563eb;
}

.code-convert-btn.docx:hover {
  background: #2563eb;
  border-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.code-convert-btn.docx:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.2);
}

/* PPT转换按钮 - 紫色 */
.code-convert-btn.ppt {
  background: #8b5cf6;
  border: 1px solid #7c3aed;
}

.code-convert-btn.ppt:hover {
  background: #7c3aed;
  border-color: #6d28d9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
}

.code-convert-btn.ppt:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(139, 92, 246, 0.2);
}

.code-convert-btn:disabled {
  background: #9ca3af;
  border-color: #6b7280;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.code-convert-btn svg {
  width: 14px;
  height: 14px;
}

.code-content {
  position: relative;
}

.code-content pre {
  margin: 0;
  padding: 16px;
  background: transparent;
  border-radius: 0;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.6;
  color: #1f2937;
}

.code-content code {
  background: transparent !important;
  padding: 0 !important;
  border-radius: 0 !important;
  font-family: inherit;
  color: inherit;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .enhanced-code-block {
    background: #1f2937;
    border-color: #374151;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .code-header {
    background: #111827;
    border-bottom-color: #374151;
  }

  .code-language {
    color: #9ca3af;
  }

  .code-copy-btn {
    background: #374151;
    border-color: #4b5563;
    color: #9ca3af;
  }

  .code-copy-btn:hover {
    background: #4b5563;
    border-color: #6b7280;
    color: #d1d5db;
  }

  .code-content pre {

/* Element Plus 复选与单选字体调整 */
.el-checkbox, .el-checkbox__label,
.el-radio, .el-radio__label,
.el-switch, .el-switch__label,
.el-select, .el-select__selected-item,
.el-form-item__label, .el-form-item__content {
  font-size: var(--font-size-small) !important; /* 16px */
}

/* 响应式缩放：手动选项 */
html.scale-compact {
  font-size: 14px; /* 紧凑模式，提高小屏可读性 */
}
html.scale-default {
  font-size: 16px; /* 默认 */
}
html.scale-large {
  font-size: 18px; /* 大号 */
}

/* 屏幕分辨率适配（自动） */
@media (max-width: 1024px) {
  :root {
    --sidebar-width: 240px;
  }
  html { font-size: 16px; }
}

@media (max-width: 800px) {
  :root {
    --sidebar-width: 200px;
    --sidebar-collapsed-width: 52px;
  }
  html { font-size: 15px; }
}

/* 极小屏幕 800x600 竖向/分辨率不足时 */
@media (max-width: 640px) {
  :root {
    --sidebar-width: 180px;
  }
  html { font-size: 14px; }
}

    color: #f9fafb;
  }
}

/* Element Plus 组件样式调整 - 调大字体和图标 */
.el-button {
  font-size: var(--font-size-base) !important;
  padding: 12px 20px !important;
  min-height: 40px !important;
}

.el-button--small {
  font-size: var(--font-size-small) !important;
  padding: 8px 16px !important;
  min-height: 32px !important;
}

.el-button--large {
  font-size: var(--font-size-large) !important;
  padding: 16px 24px !important;
  min-height: 48px !important;
}

.el-input__inner {
  font-size: var(--font-size-base) !important;
  padding: 12px 16px !important;
  min-height: 40px !important;
}

.el-textarea__inner {
  font-size: var(--font-size-base) !important;
  padding: 12px 16px !important;
  line-height: 1.6 !important;
}

.el-select .el-input__inner {
  font-size: var(--font-size-base) !important;
}

.el-option {
  font-size: var(--font-size-base) !important;
  padding: 12px 20px !important;
}

.el-slider__button {
  width: 20px !important;
  height: 20px !important;
}

.el-switch {
  font-size: var(--font-size-base) !important;
}

.el-switch__core {
  min-width: 50px !important;
  height: 24px !important;
}

.el-switch__core .el-switch__button {
  width: 20px !important;
  height: 20px !important;
}

.el-icon {
  font-size: 18px !important;
}

.el-button .el-icon {
  font-size: 18px !important;
}

.el-message {
  font-size: var(--font-size-base) !important;
  padding: 16px 20px !important;
}

.el-progress-bar__outer {
  height: 8px !important;
}

.el-progress__text {
  font-size: var(--font-size-small) !important;
}
