import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  // 说明：请将 VITE_API_BASE_URL 配置为后端根地址（不要带 /api/v1）
  // 例如 http://43.155.146.157:20010
  // 这里的各处接口已带有 /api/v1 前缀，默认 baseURL 置空，使用绝对路径发起请求，方便本地代理与生产一致
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 600000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    // 打印请求信息（开发环境）
    if (import.meta.env.DEV) {
      console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        data: config.data
      })
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 打印响应信息（开发环境）
    if (import.meta.env.DEV) {
      console.log(`✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data
      })
    }
    
    return response.data
  },
  (error) => {
    // 打印错误信息
    console.error('❌ API错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      data: error.response?.data
    })
    
    // 处理不同类型的错误
    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并重定向到登录页
          localStorage.removeItem('access_token')
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          ElMessage.error('登录已过期，请重新登录')
          break
          
        case 403:
          ElMessage.error('权限不足，无法访问该资源')
          break
          
        case 404:
          ElMessage.error('请求的资源不存在')
          break
          
        case 422:
          // 验证错误
          if (data.detail && Array.isArray(data.detail)) {
            const errorMessages = data.detail.map(err => err.msg).join(', ')
            ElMessage.error(`参数验证失败: ${errorMessages}`)
          } else {
            ElMessage.error(data.detail || '参数验证失败')
          }
          break
          
        case 429:
          ElMessage.error('请求过于频繁，请稍后重试')
          break
          
        case 500:
          ElMessage.error('服务器内部错误，请稍后重试')
          break
          
        case 502:
        case 503:
        case 504:
          ElMessage.error('服务暂时不可用，请稍后重试')
          break
          
        default:
          ElMessage.error(data.detail || data.message || `请求失败 (${status})`)
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      if (error.code === 'ECONNABORTED') {
        ElMessage.error('请求超时，请检查网络连接')
      } else {
        ElMessage.error('网络连接失败，请检查网络设置')
      }
    } else {
      // 其他错误
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 导出API实例和常用方法
export default api

// 便捷方法
export const get = (url, config = {}) => api.get(url, config)
export const post = (url, data = {}, config = {}) => api.post(url, data, config)
export const put = (url, data = {}, config = {}) => api.put(url, data, config)
export const patch = (url, data = {}, config = {}) => api.patch(url, data, config)
export const del = (url, config = {}) => api.delete(url, config)

// 文件上传方法
export const upload = (url, formData, config = {}) => {
  return api.post(url, formData, {
    ...config,
    headers: {
      'Content-Type': 'multipart/form-data',
      ...config.headers
    }
  })
}

// 下载文件方法
export const download = async (url, filename, config = {}) => {
  try {
    const response = await api.get(url, {
      ...config,
      responseType: 'blob'
    })
    
    // 创建下载链接
    const blob = new Blob([response])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
    
    return true
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败')
    return false
  }
}
