import { ref, computed } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'

// JupyterLite加载状态
const isLoading = ref(false)
const isLoaded = ref(false)
const loadingProgress = ref(0)
const loadingStage = ref('')
const pyodideWorker = ref(null)
const kernelReady = ref(false)
const messageId = ref(0)

// 加载阶段定义
const LOADING_STAGES = {
  INITIALIZING: '初始化中...',
  DOWNLOADING_CORE: '下载核心组件...',
  DOWNLOADING_PYODIDE: '下载Python运行时...',
  DOWNLOADING_PACKAGES: '下载科学计算包...',
  STARTING_KERNEL: '启动Python内核...',
  READY: '准备就绪'
}

// 渐进式加载配置
const PROGRESSIVE_LOADING_CONFIG = {
  // 核心组件 - 优先级最高
  core: {
    priority: 1,
    size: 2.5, // MB
    packages: ['@jupyterlite/kernel', '@jupyterlite/server']
  },
  // Pyodide运行时 - 必需
  pyodide: {
    priority: 2,
    size: 6.2, // MB
    packages: ['pyodide']
  },
  // 基础科学计算包 - 按需加载
  scientific: {
    priority: 3,
    size: 4.8, // MB
    packages: ['numpy', 'matplotlib', 'pandas']
  },
  // 扩展包 - 最后加载
  extended: {
    priority: 4,
    size: 3.2, // MB
    packages: ['scipy', 'plotly', 'seaborn']
  }
}

// 总大小计算
const TOTAL_SIZE = Object.values(PROGRESSIVE_LOADING_CONFIG)
  .reduce((sum, config) => sum + config.size, 0)

/**
 * JupyterLite渐进式加载Hook
 */
export function useJupyterLite() {
  
  /**
   * 开始渐进式加载JupyterLite
   * @param {boolean} showNotification 是否显示加载通知
   */
  const startProgressiveLoading = async (showNotification = true) => {
    if (isLoading.value || isLoaded.value) {
      console.log('🔄 JupyterLite已在加载中或已加载完成')
      return
    }

    console.log('🚀 开始JupyterLite渐进式加载')
    isLoading.value = true
    loadingProgress.value = 0

    let notification = null
    if (showNotification) {
      notification = ElNotification({
        title: 'Python执行环境',
        message: '正在后台加载Python执行环境，稍后即可运行代码',
        type: 'info',
        duration: 0,
        showClose: false
      })
    }

    try {
      // 阶段1: 初始化
      await loadStage('INITIALIZING', async () => {
        await simulateDelay(500) // 模拟初始化时间
      }, 5)

      // 阶段2: 加载核心组件
      await loadStage('DOWNLOADING_CORE', async () => {
        await loadJupyterLiteCore()
      }, 20)

      // 阶段3: 加载Pyodide
      await loadStage('DOWNLOADING_PYODIDE', async () => {
        await loadPyodide()
      }, 50)

      // 阶段4: 加载科学计算包
      await loadStage('DOWNLOADING_PACKAGES', async () => {
        await loadScientificPackages()
      }, 80)

      // 阶段5: 启动内核
      await loadStage('STARTING_KERNEL', async () => {
        await startKernel()
      }, 95)

      // 完成
      loadingStage.value = LOADING_STAGES.READY
      loadingProgress.value = 100
      isLoaded.value = true

      console.log('✅ JupyterLite加载完成')
      
      if (notification) {
        notification.close()
        ElNotification({
          title: 'Python执行环境',
          message: '✅ Python执行环境已准备就绪，现在可以运行代码了！',
          type: 'success',
          duration: 3000
        })
      }

    } catch (error) {
      console.error('❌ JupyterLite加载失败:', error)
      isLoading.value = false
      
      if (notification) {
        notification.close()
        ElNotification({
          title: 'Python执行环境',
          message: '❌ Python执行环境加载失败，请刷新页面重试',
          type: 'error',
          duration: 5000
        })
      }
      
      throw error
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 加载阶段辅助函数
   */
  const loadStage = async (stageName, loadFunction, targetProgress) => {
    loadingStage.value = LOADING_STAGES[stageName]
    console.log(`📦 ${loadingStage.value}`)
    
    await loadFunction()
    
    // 平滑进度更新
    await animateProgress(targetProgress)
  }

  /**
   * 平滑进度动画
   */
  const animateProgress = async (targetProgress) => {
    const startProgress = loadingProgress.value
    const duration = 300 // ms
    const steps = 20
    const stepSize = (targetProgress - startProgress) / steps
    const stepDuration = duration / steps

    for (let i = 0; i < steps; i++) {
      loadingProgress.value = Math.min(startProgress + stepSize * (i + 1), targetProgress)
      await simulateDelay(stepDuration)
    }
  }

  /**
   * 加载JupyterLite核心
   */
  const loadJupyterLiteCore = async () => {
    // 预加载必要的模块
    await simulateNetworkLoad(PROGRESSIVE_LOADING_CONFIG.core.size)
    console.log('📦 JupyterLite核心组件加载完成')
  }

  /**
   * 创建和初始化Pyodide Worker
   */
  const loadPyodide = async () => {
    try {
      console.log('🐍 创建Pyodide Worker...')

      // 创建Web Worker
      pyodideWorker.value = new Worker('/pyodide-worker.js')

      // 设置Worker消息处理
      setupWorkerMessageHandling()

      // 初始化Pyodide
      await sendWorkerMessage('INIT_PYODIDE', {})

      console.log('✅ Pyodide Worker创建完成')
    } catch (error) {
      console.error('❌ Pyodide Worker创建失败:', error)
      throw error
    }
  }

  /**
   * 加载科学计算包（在Worker中已完成）
   */
  const loadScientificPackages = async () => {
    // 科学计算包在Worker初始化时已加载
    console.log('📊 科学计算包已在Worker中加载完成')
    await simulateNetworkLoad(PROGRESSIVE_LOADING_CONFIG.scientific.size)
  }

  /**
   * 启动内核（在Worker中已完成）
   */
  const startKernel = async () => {
    // 内核在Worker初始化时已启动
    console.log('⚡ Python内核已在Worker中启动完成')
    kernelReady.value = true
    await simulateDelay(500)
  }

  /**
   * 模拟网络加载延迟
   */
  const simulateNetworkLoad = async (sizeMB) => {
    // 模拟网络速度 2MB/s
    const networkSpeedMBps = 2
    const loadTime = (sizeMB / networkSpeedMBps) * 1000
    await simulateDelay(loadTime)
  }

  /**
   * 设置Worker消息处理
   */
  const setupWorkerMessageHandling = () => {
    if (!pyodideWorker.value) return

    pyodideWorker.value.onmessage = (e) => {
      const { type, data, id, result, error } = e.data

      switch (type) {
        case 'INIT_COMPLETE':
          console.log('✅ Worker初始化完成')
          break

        case 'STDOUT':
          console.log('Python输出:', data.text)
          break

        case 'STDERR':
          console.error('Python错误:', data.text)
          break

        case 'EXECUTION_RESULT':
          // 处理执行结果
          handleExecutionResult(id, result)
          break

        case 'ERROR':
        case 'WORKER_ERROR':
          console.error('Worker错误:', error)
          handleExecutionError(id, error)
          break
      }
    }

    pyodideWorker.value.onerror = (error) => {
      console.error('Worker错误:', error)
    }
  }

  /**
   * 发送消息到Worker
   */
  const sendWorkerMessage = (type, data) => {
    return new Promise((resolve, reject) => {
      if (!pyodideWorker.value) {
        reject(new Error('Worker未初始化'))
        return
      }

      const id = ++messageId.value
      const timeout = setTimeout(() => {
        reject(new Error('Worker消息超时'))
      }, 30000) // 30秒超时

      // 存储回调
      const handleResponse = (e) => {
        if (e.data.id === id) {
          clearTimeout(timeout)
          pyodideWorker.value.removeEventListener('message', handleResponse)

          if (e.data.type === 'ERROR' || e.data.type === 'WORKER_ERROR') {
            reject(new Error(e.data.error.message))
          } else {
            resolve(e.data)
          }
        }
      }

      pyodideWorker.value.addEventListener('message', handleResponse)
      pyodideWorker.value.postMessage({ type, data, id })
    })
  }

  /**
   * 模拟延迟
   */
  const simulateDelay = (ms) => {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 存储执行回调
  const executionCallbacks = new Map()

  /**
   * 处理执行结果
   */
  const handleExecutionResult = (id, result) => {
    const callback = executionCallbacks.get(id)
    if (callback) {
      executionCallbacks.delete(id)
      callback.resolve(result)
    }
  }

  /**
   * 处理执行错误
   */
  const handleExecutionError = (id, error) => {
    const callback = executionCallbacks.get(id)
    if (callback) {
      executionCallbacks.delete(id)
      callback.reject(new Error(error.message))
    }
  }

  /**
   * 执行Python代码
   */
  const executePythonCode = async (code) => {
    if (!isLoaded.value || !kernelReady.value) {
      throw new Error('Python执行环境尚未准备就绪')
    }

    if (!pyodideWorker.value) {
      throw new Error('Pyodide Worker未初始化')
    }

    console.log('🐍 执行Python代码:', code.substring(0, 100) + '...')

    try {
      const startTime = Date.now()

      // 发送执行请求到Worker
      const response = await sendWorkerMessage('EXECUTE_CODE', { code })
      const endTime = Date.now()

      const result = response.result

      // 简化的调试信息，避免影响主程序
      console.log('🔍 Worker返回结果 - 图片数量:', result.images ? result.images.length : 0)

      const processedResult = {
        stdout: result.stdout || '',
        stderr: result.stderr || '',
        images: result.images || [],
        execTimeMs: endTime - startTime,
        success: result.success !== false
      }

      return processedResult

    } catch (error) {
      console.error('❌ Python代码执行失败:', error)
      return {
        stdout: '',
        stderr: error.message || '代码执行出现未知错误',
        images: [],
        execTimeMs: 0,
        success: false
      }
    }
  }

  /**
   * 检查Pyodide是否已准备就绪
   */
  const isPyodideReady = () => {
    return pyodideWorker.value !== null && kernelReady.value
  }

  /**
   * 检查是否可以执行代码
   */
  const canExecuteCode = computed(() => {
    return isLoaded.value && !isLoading.value && isPyodideReady()
  })

  /**
   * 获取加载状态信息
   */
  const getLoadingStatus = computed(() => {
    return {
      isLoading: isLoading.value,
      isLoaded: isLoaded.value,
      progress: loadingProgress.value,
      stage: loadingStage.value,
      canExecute: canExecuteCode.value
    }
  })

  return {
    // 状态
    isLoading: computed(() => isLoading.value),
    isLoaded: computed(() => isLoaded.value),
    loadingProgress: computed(() => loadingProgress.value),
    loadingStage: computed(() => loadingStage.value),
    canExecuteCode,
    getLoadingStatus,

    // 方法
    startProgressiveLoading,
    executePythonCode
  }
}
