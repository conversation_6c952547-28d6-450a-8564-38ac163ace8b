import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// PDF处理配置
const PDF_CONFIG = {
  thumbnail: {
    scale: 0.3,
    maxWidth: 160,
    quality: 0.8,
  },
  export: {
    scale: 2.0,
    format: 'png',
    quality: 0.95,
  },
  performance: {
    maxConcurrentRenders: 2,
    cacheSize: 50,
    lazyLoadBuffer: 5,
  }
}

export function usePdfProcessor() {
  // 状态管理
  const isLoading = ref(false)
  const isReady = ref(false)
  const isInitializing = ref(false)
  const currentPdf = ref(null)
  const pdfDocument = ref(null)
  const pages = ref([])
  const selectedPages = ref(new Set())
  const thumbnailCache = reactive(new Map())

  // 多PDF管理
  const pdfList = ref([])
  const currentPdfId = ref(null)

  // PDF.js实例
  let pdfjsLib = null

  // 使用 CDN 加载 PDF.js（避免 npm 包的模块解析问题）
  const initializePdfJs = async () => {
    if (isReady.value || isInitializing.value) return

    isInitializing.value = true
    console.log('🔧 开始后台初始化PDF.js (CDN方式)...')

    try {
      // 检查是否已经通过 CDN 加载了 PDF.js
      if (window.pdfjsLib) {
        pdfjsLib = window.pdfjsLib
        console.log('✅ 发现已加载的 PDF.js CDN 版本')
      } else {
        // 动态加载 PDF.js CDN 脚本
        await loadPdfJsFromCDN()
        pdfjsLib = window.pdfjsLib
      }

      console.log('🔍 PDF.js CDN 模块信息:', {
        hasGlobalWorkerOptions: !!pdfjsLib.GlobalWorkerOptions,
        hasGetDocument: !!pdfjsLib.getDocument,
        version: pdfjsLib.version || 'unknown'
      })

      // 设置 worker 路径（CDN 版本 2.11.338 - 最稳定版本，无私有字段问题）
      const workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.worker.min.js'
      pdfjsLib.GlobalWorkerOptions.workerSrc = workerSrc
      console.log('🧵 PDF.js CDN workerSrc 已设置 (v2.11.338):', workerSrc)

      // 设置全局配置
      if (pdfjsLib.VerbosityLevel) {
        pdfjsLib.GlobalWorkerOptions.verbosity = pdfjsLib.VerbosityLevel.ERRORS
      }

      isReady.value = true
      console.log('✅ PDF.js CDN 初始化完成 (v2.11.338)')
    } catch (error) {
      console.error('❌ PDF.js CDN 初始化失败:', error)
      ElMessage.error('PDF处理器初始化失败，请检查网络连接')
    } finally {
      isInitializing.value = false
    }
  }

  // 动态加载 PDF.js CDN 脚本
  const loadPdfJsFromCDN = () => {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载
      if (window.pdfjsLib) {
        resolve()
        return
      }

      const script = document.createElement('script')
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.min.js'
      script.onload = () => {
        console.log('📦 PDF.js CDN 脚本加载完成')
        // 等待一小段时间确保全局变量可用
        setTimeout(() => {
          if (window.pdfjsLib) {
            resolve()
          } else {
            reject(new Error('PDF.js CDN 加载后未找到全局变量'))
          }
        }, 100)
      }
      script.onerror = () => {
        reject(new Error('PDF.js CDN 脚本加载失败'))
      }
      document.head.appendChild(script)
    })
  }

  // 加载PDF文件
  const loadPdf = async (file) => {
    if (!isReady.value) {
      console.log('🔧 PDF处理器未就绪，先进行初始化...')
      await initializePdfJs()
      if (!isReady.value) {
        ElMessage.error('PDF处理器初始化失败')
        return false
      }
    }

    if (!file || file.type !== 'application/pdf') {
      ElMessage.error('请选择有效的PDF文件')
      return false
    }

    isLoading.value = true
    console.log('📄 开始加载PDF:', file.name, file.size)

    try {
      // 创建对象URL
      const fileUrl = URL.createObjectURL(file)

      // 检查 getDocument 函数是否存在
      if (!pdfjsLib || typeof pdfjsLib.getDocument !== 'function') {
        throw new Error(`pdfjsLib.getDocument 不是函数。pdfjsLib 类型: ${typeof pdfjsLib}, getDocument 类型: ${typeof pdfjsLib?.getDocument}`)
      }

      // 解析PDF文档，使用 2.11.338 兼容的配置
      const loadingTask = pdfjsLib.getDocument({
        url: fileUrl,
        // 禁用 worker，在主线程渲染（避免私有字段问题）
        disableWorker: true,
        // 禁用字体渲染以避免兼容性问题
        disableFontFace: true,
        // 设置最大图像大小
        maxImageSize: 16777216, // 16MB
        // 禁用流式解析以提高兼容性
        disableStream: true,
        // 禁用范围请求
        disableRange: true
      })

      const pdf = await loadingTask.promise

      // 验证PDF文档对象
      if (!pdf || typeof pdf.getPage !== 'function') {
        throw new Error('PDF文档对象无效')
      }

      // 生成PDF ID
      const pdfId = Date.now().toString()

      // 保存文档信息
      pdfDocument.value = pdf
      const pdfInfo = {
        id: pdfId,
        file,
        name: file.name,
        size: file.size,
        totalPages: pdf.numPages,
        url: fileUrl,
        loadTime: new Date()
      }

      currentPdf.value = pdfInfo
      currentPdfId.value = pdfId

      // 添加到PDF列表（如果不存在）
      const existingIndex = pdfList.value.findIndex(p => p.name === file.name && p.size === file.size)
      if (existingIndex >= 0) {
        // 更新现有PDF
        pdfList.value[existingIndex] = pdfInfo
      } else {
        // 添加新PDF
        pdfList.value.push(pdfInfo)
      }

      // 初始化页面信息
      pages.value = Array.from({ length: pdf.numPages }, (_, i) => ({
        num: i + 1,
        rendered: false,
        thumbnailUrl: null,
        loading: false
      }))

      // 清空选择和缓存
      selectedPages.value.clear()
      thumbnailCache.clear()

      console.log(`✅ PDF加载成功: ${pdf.numPages}页`)
      ElMessage.success(`PDF加载成功，共${pdf.numPages}页`)

      // 等待一小段时间确保PDF文档完全就绪
      await new Promise(resolve => setTimeout(resolve, 100))

      return true
    } catch (error) {
      console.error('❌ PDF加载失败:', error)
      ElMessage.error('PDF文件加载失败: ' + (error.message || '请检查文件是否损坏'))
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 懒渲染缩略图（简化版本，避免私有字段访问）
  const renderThumbnail = async (pageNum, scale = PDF_CONFIG.thumbnail.scale) => {
    if (!pdfDocument.value) {
      console.warn(`⚠️ PDF文档未加载，无法渲染第${pageNum}页`)
      return null
    }

    const cacheKey = `${pageNum}-${scale}`
    if (thumbnailCache.has(cacheKey)) {
      return thumbnailCache.get(cacheKey)
    }

    const pageIndex = pageNum - 1
    if (pageIndex < 0 || pageIndex >= pages.value.length) {
      console.warn(`⚠️ 页码超出范围: ${pageNum}，总页数: ${pages.value.length}`)
      return null
    }

    const pageInfo = pages.value[pageIndex]
    if (pageInfo.loading) return null

    pageInfo.loading = true
    console.log(`🖼️ 渲染缩略图: 第${pageNum}页`)

    try {
      // 确保 PDF 文档完全加载并可访问
      if (!pdfDocument.value || typeof pdfDocument.value.getPage !== 'function') {
        throw new Error('PDF文档对象无效或未完全初始化')
      }

      // 获取页面，添加重试机制以处理私有字段错误
      let page
      let retryCount = 0
      const maxRetries = 3

      while (retryCount < maxRetries) {
        try {
          page = await pdfDocument.value.getPage(pageNum)
          break
        } catch (pageError) {
          retryCount++
          if (pageError.message.includes('private member') || pageError.message.includes('private field')) {
            console.warn(`⚠️ 私有字段访问错误第${pageNum}页，重试 ${retryCount}/${maxRetries}`)
            // 对于私有字段错误，等待更长时间
            await new Promise(resolve => setTimeout(resolve, 200 * retryCount))
          } else {
            console.warn(`⚠️ 获取第${pageNum}页失败，重试 ${retryCount}/${maxRetries}:`, pageError.message)
            await new Promise(resolve => setTimeout(resolve, 100 * retryCount))
          }
          if (retryCount >= maxRetries) {
            // 如果是私有字段错误且重试失败，尝试最后的备用方案
            if (pageError.message.includes('private member') || pageError.message.includes('private field')) {
              console.warn(`⚠️ 私有字段错误持续出现，尝试备用渲染方案第${pageNum}页`)
              // 返回一个占位符，避免阻塞其他页面
              return await createPlaceholderThumbnail(pageNum)
            }
            throw new Error(`无法获取第${pageNum}页: ${pageError.message}`)
          }
        }
      }

      if (!page) {
        throw new Error(`无法获取第${pageNum}页`)
      }

      // 计算视口
      const viewport = page.getViewport({ scale })

      // 创建canvas
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.height = viewport.height
      canvas.width = viewport.width

      // 渲染页面
      const renderContext = {
        canvasContext: context,
        viewport: viewport
      }

      await page.render(renderContext).promise

      // 转换为blob URL
      const blob = await new Promise(resolve => {
        canvas.toBlob(resolve, 'image/jpeg', PDF_CONFIG.thumbnail.quality)
      })

      if (!blob) {
        throw new Error('Canvas转换为Blob失败')
      }

      const thumbnailUrl = URL.createObjectURL(blob)

      // 缓存结果
      thumbnailCache.set(cacheKey, thumbnailUrl)
      pageInfo.thumbnailUrl = thumbnailUrl
      pageInfo.rendered = true

      console.log(`✅ 缩略图渲染完成: 第${pageNum}页`)
      return thumbnailUrl
    } catch (error) {
      console.error(`❌ 缩略图渲染失败: 第${pageNum}页`, error)
      // 标记为渲染失败，但不阻塞其他页面
      pageInfo.rendered = false
      return null
    } finally {
      pageInfo.loading = false
    }
  }

  // 创建占位符缩略图（当私有字段错误无法解决时）
  const createPlaceholderThumbnail = async (pageNum) => {
    try {
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.width = 160
      canvas.height = 200

      // 绘制占位符背景
      context.fillStyle = '#f5f5f5'
      context.fillRect(0, 0, canvas.width, canvas.height)

      // 绘制边框
      context.strokeStyle = '#ddd'
      context.lineWidth = 2
      context.strokeRect(1, 1, canvas.width - 2, canvas.height - 2)

      // 绘制页码文字
      context.fillStyle = '#666'
      context.font = '16px Arial'
      context.textAlign = 'center'
      context.fillText(`第 ${pageNum} 页`, canvas.width / 2, canvas.height / 2 - 10)
      context.fillText('预览不可用', canvas.width / 2, canvas.height / 2 + 10)

      // 转换为blob URL
      const blob = await new Promise(resolve => {
        canvas.toBlob(resolve, 'image/png', 0.8)
      })

      if (!blob) {
        throw new Error('占位符Canvas转换为Blob失败')
      }

      const thumbnailUrl = URL.createObjectURL(blob)

      // 缓存占位符结果
      const cacheKey = `${pageNum}-${PDF_CONFIG.thumbnail.scale}`
      thumbnailCache.set(cacheKey, thumbnailUrl)

      const pageInfo = pages.value[pageNum - 1]
      pageInfo.thumbnailUrl = thumbnailUrl
      pageInfo.rendered = true

      console.log(`✅ 占位符缩略图创建完成: 第${pageNum}页`)
      return thumbnailUrl
    } catch (error) {
      console.error(`❌ 占位符缩略图创建失败: 第${pageNum}页`, error)
      return null
    }
  }

  // 批量渲染可见范围的缩略图
  const renderVisibleThumbnails = async (startPage, endPage) => {
    if (!pdfDocument.value) return

    const renderPromises = []
    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
      if (pageNum > 0 && pageNum <= pages.value.length) {
        const pageInfo = pages.value[pageNum - 1]
        if (!pageInfo.rendered && !pageInfo.loading) {
          renderPromises.push(renderThumbnail(pageNum))
        }
      }
    }

    if (renderPromises.length > 0) {
      await Promise.allSettled(renderPromises)
    }
  }

  // 页面选择控制
  const togglePage = (pageNum) => {
    if (selectedPages.value.has(pageNum)) {
      selectedPages.value.delete(pageNum)
    } else {
      selectedPages.value.add(pageNum)
    }
  }

  const selectAll = () => {
    selectedPages.value.clear()
    for (let i = 1; i <= pages.value.length; i++) {
      selectedPages.value.add(i)
    }
  }

  const selectNone = () => {
    selectedPages.value.clear()
  }

  // 页码范围选择
  const selectPageRange = (startPage, endPage) => {
    selectedPages.value.clear()
    const start = Math.max(1, Math.min(startPage, endPage))
    const end = Math.min(pages.value.length, Math.max(startPage, endPage))

    for (let i = start; i <= end; i++) {
      selectedPages.value.add(i)
    }

    console.log(`📄 选择页码范围: ${start}-${end}, 共${selectedPages.value.size}页`)
  }

  // 切换到指定PDF
  const switchToPdf = async (pdfId) => {
    const targetPdf = pdfList.value.find(p => p.id === pdfId)
    if (!targetPdf) {
      throw new Error(`未找到PDF: ${pdfId}`)
    }

    if (currentPdfId.value === pdfId) {
      console.log('📄 已经是当前PDF，无需切换')
      return
    }

    console.log('🔄 切换PDF:', targetPdf.name)

    // 重新加载PDF文档
    isLoading.value = true
    try {
      const loadingTask = pdfjsLib.getDocument({
        url: targetPdf.url,
        disableWorker: true,
        disableFontFace: true,
        maxImageSize: 16777216,
        disableStream: true,
        disableRange: true
      })

      const pdf = await loadingTask.promise

      // 更新当前状态
      pdfDocument.value = pdf
      currentPdf.value = targetPdf
      currentPdfId.value = pdfId

      // 重新初始化页面信息
      pages.value = Array.from({ length: pdf.numPages }, (_, i) => ({
        num: i + 1,
        rendered: false,
        thumbnailUrl: null,
        loading: false
      }))

      // 清空选择
      selectedPages.value.clear()

      console.log(`✅ PDF切换成功: ${pdf.numPages}页`)
      return true
    } catch (error) {
      console.error('❌ PDF切换失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 导出选中页面为图片
  const exportSelectedPages = async (onProgress) => {
    if (!pdfDocument.value || selectedPages.value.size === 0) {
      ElMessage.warning('请先选择要导出的页面')
      return []
    }

    const selectedArray = Array.from(selectedPages.value).sort((a, b) => a - b)
    const exportedFiles = []
    let completed = 0

    console.log(`🚀 开始导出${selectedArray.length}页为图片`)

    for (const pageNum of selectedArray) {
      try {
        // 高分辨率渲染
        const page = await pdfDocument.value.getPage(pageNum)
        const viewport = page.getViewport({ scale: PDF_CONFIG.export.scale })
        
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        canvas.height = viewport.height
        canvas.width = viewport.width

        const renderContext = {
          canvasContext: context,
          viewport: viewport
        }
        
        await page.render(renderContext).promise

        // 转换为File对象
        const blob = await new Promise(resolve => {
          canvas.toBlob(resolve, `image/${PDF_CONFIG.export.format}`, PDF_CONFIG.export.quality)
        })

        const fileName = `${currentPdf.value.name.replace('.pdf', '')}_page-${pageNum.toString().padStart(3, '0')}.${PDF_CONFIG.export.format}`
        const file = new File([blob], fileName, { type: blob.type })
        
        exportedFiles.push(file)
        completed++

        // 进度回调
        if (onProgress) {
          onProgress(completed, selectedArray.length)
        }

        console.log(`✅ 导出完成: 第${pageNum}页`)
      } catch (error) {
        console.error(`❌ 导出失败: 第${pageNum}页`, error)
      }
    }

    console.log(`🎉 导出完成，共${exportedFiles.length}个文件`)
    return exportedFiles
  }

  // 清理资源
  const cleanup = () => {
    console.log('🧹 清理PDF资源')
    
    // 清理对象URL
    if (currentPdf.value?.url) {
      URL.revokeObjectURL(currentPdf.value.url)
    }

    // 清理缩略图URL
    for (const url of thumbnailCache.values()) {
      URL.revokeObjectURL(url)
    }

    // 重置状态
    currentPdf.value = null
    pdfDocument.value = null
    pages.value = []
    selectedPages.value.clear()
    thumbnailCache.clear()


  }

  return {
    // 状态
    isLoading,
    isReady,
    isInitializing,
    currentPdf,
    pages,
    selectedPages,
    pdfList,
    currentPdfId,

    // 方法
    initializePdfJs,
    loadPdf,
    renderThumbnail,
    renderVisibleThumbnails,
    togglePage,
    selectAll,
    selectNone,
    selectPageRange,
    switchToPdf,
    exportSelectedPages,
    cleanup
  }
}
