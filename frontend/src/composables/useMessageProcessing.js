import { ref } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import katex from 'katex'

export function useMessageProcessing() {
  // 处理消息内容，为代码块添加语法高亮
  const processMessageContentWithCodeBlocks = (htmlContent) => {
    if (!htmlContent) return htmlContent

    // 使用字符串替换的方式处理代码块
    let processedContent = htmlContent

    // 为代码块添加语法高亮
    processedContent = processedContent.replace(
      /<pre><code([^>]*)>([\s\S]*?)<\/code><\/pre>/g, 
      (_, attributes, codeContent) => {
        // 提取语言信息
        const languageMatch = attributes.match(/class="language-([a-zA-Z0-9_+\-#]+)"/) || 
                             attributes.match(/class="([a-zA-Z0-9_+\-#]+)"/)
        const language = languageMatch ? languageMatch[1] : ''

        // 提取纯文本内容
        let plainText = ''

        try {
          // 处理HTML实体和标签，保持换行符
          plainText = codeContent
            .replace(/<br\s*\/?>/gi, '\n')  // 将<br>转换为换行符
            .replace(/<[^>]*>/g, '')        // 移除所有HTML标签
            .replace(/&lt;/g, '<')          // 解码HTML实体
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/&nbsp;/g, ' ')

          // 如果没有换行符，尝试DOM解析
          if (!plainText.includes('\n') && codeContent.includes('\n')) {
            try {
              const tempDiv = document.createElement('div')
              tempDiv.innerHTML = codeContent.replace(/<br\s*\/?>/gi, '\n')
              plainText = tempDiv.textContent || tempDiv.innerText || ''
            } catch (domError) {
              console.warn('DOM解析失败:', domError)
            }
          }

          // 确保有内容
          if (!plainText || plainText.trim() === '') {
            plainText = codeContent.replace(/<[^>]*>/g, '') || codeContent
          }

          // 清理多余的空白，但保持换行符
          plainText = plainText.replace(/\r\n/g, '\n').replace(/\r/g, '\n')

        } catch (error) {
          console.warn('代码内容提取失败，使用原始内容:', error)
          plainText = codeContent.replace(/<[^>]*>/g, '') || codeContent
        }

        // 应用语法高亮
        let highlightedCode = ''
        try {
          if (language && hljs.getLanguage(language)) {
            // 如果指定了语言且支持，使用指定语言高亮
            highlightedCode = hljs.highlight(plainText, { language }).value
          } else {
            // 否则尝试自动检测语言
            const result = hljs.highlightAuto(plainText)
            highlightedCode = result.value
          }
        } catch (e) {
          console.warn('代码高亮失败:', e)
          highlightedCode = plainText // 降级到纯文本
        }

        // 为复制功能编码代码内容
        const encodedCode = encodeURIComponent(plainText)

        // 检查代码类型，决定显示哪些按钮
        const isPython = language && language.toLowerCase() === 'python'
        const isHtml = language && ['html', 'htm'].includes(language.toLowerCase())
        const isMarkdown = language && ['markdown', 'md'].includes(language.toLowerCase())

        return `
          <div class="enhanced-code-block">
            <div class="code-header">
              ${language ? `<span class="code-language">${language.toUpperCase()}</span>` : ''}
              <div class="code-actions">
                ${isPython ? `
                  <button class="code-run-btn python" data-code="${encodedCode}" title="运行Python代码">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <polygon points="5,3 19,12 5,21"></polygon>
                    </svg>
                    <span class="run-text">运行</span>
                  </button>
                ` : ''}
                ${isHtml ? `
                  <button class="code-run-btn html" data-code="${encodedCode}" title="在浏览器中运行HTML代码">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                      <polyline points="14,2 14,8 20,8"></polyline>
                      <line x1="16" y1="13" x2="8" y2="13"></line>
                      <line x1="16" y1="17" x2="8" y2="17"></line>
                      <polyline points="10,9 9,9 8,9"></polyline>
                    </svg>
                    <span class="run-text">运行</span>
                  </button>
                ` : ''}
                ${isMarkdown ? `
                  <button class="code-convert-btn docx" data-code="${encodedCode}" title="转换为DOCX文档">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                      <polyline points="14,2 14,8 20,8"></polyline>
                      <line x1="12" y1="18" x2="12" y2="12"></line>
                      <line x1="9" y1="15" x2="15" y2="15"></line>
                    </svg>
                    <span class="convert-text">DOCX</span>
                  </button>
                  <button class="code-convert-btn ppt" data-code="${encodedCode}" title="转换为PPT演示文稿">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <rect x="3" y="4" width="18" height="12" rx="1" ry="1"></rect>
                      <line x1="7" y1="8" x2="17" y2="8"></line>
                      <line x1="7" y1="12" x2="17" y2="12"></line>
                      <line x1="7" y1="16" x2="13" y2="16"></line>
                    </svg>
                    <span class="convert-text">PPT</span>
                  </button>
                ` : ''}
                <button class="code-copy-btn" data-code="${encodedCode}" title="复制代码">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                  <span class="copy-text">复制</span>
                </button>
              </div>
            </div>
            <div class="code-content">
              <pre><code class="hljs ${language ? `language-${language}` : ''}">${highlightedCode}</code></pre>
            </div>
          </div>
        `
      }
    )

    return processedContent
  }

  // 处理LaTeX渲染
  const processLatexContent = (content, enabled = true) => {
    if (!enabled || !content) return content

    try {
      // 处理块级LaTeX: $$...$$（必须先处理，避免与行内公式冲突）
      content = content.replace(/\$\$([\s\S]+?)\$\$/g, (match, latex) => {
        try {
          const rendered = katex.renderToString(latex.trim(), {
            displayMode: true,
            throwOnError: false
          })
          return `<div class="math-block">${rendered}</div>`
        } catch (e) {
          console.warn('块级LaTeX渲染失败:', e)
          return `<div class="math-block math-error">$$${latex}$$</div>`
        }
      })

      // 处理行内LaTeX: $...$
      content = content.replace(/\$([^$\n]+)\$/g, (match, latex) => {
        try {
          const rendered = katex.renderToString(latex.trim(), {
            displayMode: false,
            throwOnError: false
          })
          return `<span class="math-inline">${rendered}</span>`
        } catch (e) {
          console.warn('行内LaTeX渲染失败:', e)
          return `<span class="math-inline math-error">$${latex}$</span>`
        }
      })
    } catch (error) {
      console.warn('LaTeX处理失败:', error)
    }

    return content
  }

  // 智能解析嵌套代码围栏 - 从最外层开始解析
  const parseNestedCodeFences = (content) => {
    const codeFencePlaceholders = []
    const lines = content.split('\n')
    const result = []

    let i = 0
    while (i < lines.length) {
      const line = lines[i]
      const fenceMatch = line.match(/^(\s*)(```+)([a-zA-Z0-9_+\-#]*)(.*)$/)

      if (fenceMatch) {
        const [, indent, fence, language] = fenceMatch
        const fenceLength = fence.length

        // 找到开始围栏，现在寻找匹配的结束围栏
        const blockContent = []
        let j = i + 1
        let found = false

        // 注意：Markdown规范中，围栏代码块内部不应再解析嵌套围栏。
        // 因此，这里仅识别与外层“相同长度”的结束围栏；
        // 任何其他数量的反引号（如内层的 ```）都作为普通内容保留。
        while (j < lines.length) {
          const currentLine = lines[j]

          // 只当反引号后仅有空白时，才认为是结束；且长度必须与外层完全相等，缩进一致
          const endFenceMatch = currentLine.match(/^(\s*)(```+)(\s*)$/)
          if (endFenceMatch) {
            const [, endIndent, endFence] = endFenceMatch
            const endFenceLength = endFence.length

            if (endFenceLength === fenceLength && endIndent === indent) {
              found = true
              break
            }
          }

          // 其余情况（包括出现 ``` 或 ````` 等不同长度的反引号行），均视作普通内容
          blockContent.push(currentLine)
          j++
        }

        if (found) {
          // 成功找到完整的代码块
          const index = codeFencePlaceholders.length
          const placeholder = `CODEFENCEPLACEHOLDER${index}CODEFENCEPLACEHOLDER`
          codeFencePlaceholders.push({
            placeholder,
            language: language || '',
            content: blockContent.join('\n'),
            indent
          })
          result.push(placeholder)
          i = j + 1 // 跳过结束围栏
        } else {
          // 没有找到结束围栏，作为普通行处理
          result.push(line)
          i++
        }
      } else {
        // 普通行
        result.push(line)
        i++
      }
    }

    return {
      content: result.join('\n'),
      placeholders: codeFencePlaceholders
    }
  }

  // 处理Markdown内容（保护LaTeX公式和嵌套代码围栏）
  const processMarkdownContent = (content, enableLatex = true) => {
    if (!content) return ''

    try {
      // 先保护嵌套的代码围栏
      const { content: protectedContent, placeholders: codeFencePlaceholders } = parseNestedCodeFences(content)

      console.log('=== processMarkdownContent Debug ===')
      console.log('原始内容:', content)
      console.log('保护后内容:', protectedContent)
      console.log('代码围栏占位符:', codeFencePlaceholders)

      // 保护LaTeX公式，避免被Markdown处理
      const latexPlaceholders = []
      let finalContent = protectedContent

      if (enableLatex) {
        // 保护块级LaTeX: $$...$$
        finalContent = finalContent.replace(/\$\$([\s\S]+?)\$\$/g, (_, latex) => {
          const index = latexPlaceholders.length
          const placeholder = `LATEXBLOCKPLACEHOLDER${index}LATEXBLOCKPLACEHOLDER`
          latexPlaceholders.push({ type: 'block', content: latex.trim(), placeholder })
          return placeholder
        })

        // 保护行内LaTeX: $...$
        finalContent = finalContent.replace(/\$([^$\n]+)\$/g, (_, latex) => {
          const index = latexPlaceholders.length
          const placeholder = `LATEXINLINEPLACEHOLDER${index}LATEXINLINEPLACEHOLDER`
          latexPlaceholders.push({ type: 'inline', content: latex.trim(), placeholder })
          return placeholder
        })
      }

      // 配置marked选项
      marked.setOptions({
        highlight: function(code, lang) {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return hljs.highlight(code, { language: lang }).value
            } catch (err) {
              console.warn('代码高亮失败:', err)
            }
          }
          return hljs.highlightAuto(code).value
        },
        breaks: true,
        gfm: true
      })

      console.log('准备传给marked的内容:', finalContent)

      // 处理Markdown
      let processed = marked(finalContent)

      console.log('marked处理后的内容:', processed)

      // 为图片添加样式类和点击处理
      processed = processed.replace(
        /<img([^>]*?)src="([^"]*)"([^>]*?)>/g,
        '<img$1src="$2"$3 class="message-image" style="max-width: 100%; max-height: 400px; cursor: pointer; border-radius: 8px; margin: 8px 0;">'
      )

      // 先恢复LaTeX公式并渲染（避免干扰代码块包装结构）
      if (enableLatex) {
        latexPlaceholders.forEach((item) => {
          try {
            const rendered = katex.renderToString(item.content, {
              displayMode: item.type === 'block',
              throwOnError: false
            })

            const wrapper = item.type === 'block'
              ? `<div class="math-block">${rendered}</div>`
              : `<span class="math-inline">${rendered}</span>`

            // 使用全局替换确保所有占位符都被替换
            processed = processed.replaceAll(item.placeholder, wrapper)
          } catch (e) {
            console.warn('LaTeX渲染失败:', e, 'content:', item.content)
            const fallback = item.type === 'block'
              ? `<div class="math-block math-error">$$${item.content}$$</div>`
              : `<span class="math-inline math-error">$${item.content}$</span>`
            processed = processed.replaceAll(item.placeholder, fallback)
          }
        })
      } else {
        // 如果禁用LaTeX渲染，恢复原始LaTeX代码
        latexPlaceholders.forEach((item) => {
          const original = item.type === 'block'
            ? `$$${item.content}$$`
            : `$${item.content}$`
          processed = processed.replaceAll(item.placeholder, original)
        })
      }

      // 再恢复代码围栏占位符为实际的代码块
      codeFencePlaceholders.forEach((item, index) => {
        console.log(`恢复占位符 ${index}:`, item)

        // 检查占位符的各种可能形式
        const originalPlaceholder = item.placeholder
        let actualPlaceholder = originalPlaceholder
        let isWrapped = false

        // 检查原始占位符
        if (processed.includes(originalPlaceholder)) {
          actualPlaceholder = originalPlaceholder
        }
        // 检查是否被<p>标签包装
        else if (processed.includes(`<p>${originalPlaceholder}</p>`)) {
          actualPlaceholder = `<p>${originalPlaceholder}</p>`
          isWrapped = true
        }
        // 检查是否被其他标签包装
        else {
          // 使用正则表达式查找被包装的占位符
          const wrappedMatch = processed.match(new RegExp(`<([^>]+)>${originalPlaceholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}</([^>]+)>`))
          if (wrappedMatch) {
            actualPlaceholder = wrappedMatch[0]
            isWrapped = true
          } else {
            console.warn(`占位符 ${originalPlaceholder} 在processed中未找到!`)
            console.log('当前processed内容:', processed)
            return
          }
        }

        console.log(`找到占位符形式: ${actualPlaceholder}, 是否被包装: ${isWrapped}`)

        // HTML转义代码内容
        const escapedContent = item.content
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#39;')

        // 创建标准的HTML代码块结构
        const languageClass = item.language ? ` class="language-${item.language}"` : ''
        const codeBlock = `<pre><code${languageClass}>${escapedContent}</code></pre>`

        console.log(`替换占位符 ${actualPlaceholder} 为:`, codeBlock)

        // 使用全局替换
        const beforeReplace = processed
        processed = processed.split(actualPlaceholder).join(codeBlock)

        if (beforeReplace === processed) {
          console.warn(`占位符 ${actualPlaceholder} 替换失败，内容未改变`)
        } else {
          console.log(`占位符 ${actualPlaceholder} 替换成功`)
        }
      })

      console.log('恢复占位符后的内容:', processed)

      // 最后将标准的 <pre><code> 转换为带按钮和语言标识的增强结构
      processed = processMessageContentWithCodeBlocks(processed)

      return processed
    } catch (error) {
      console.warn('Markdown处理失败:', error)
      return content
    }
  }

  // 综合处理消息内容
  const processMessage = (content, options = {}) => {
    if (!content) return ''

    const {
      enableLatex = true,
      enableMarkdown = true
    } = options

    // 如果启用Markdown，使用集成的Markdown处理（包含LaTeX和代码高亮）
    if (enableMarkdown && typeof content === 'string') {
      return processMarkdownContent(content, enableLatex)
    }

    // 否则只处理LaTeX
    if (enableLatex) {
      return processLatexContent(content, enableLatex)
    }

    return content
  }

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp) return ''
    
    try {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date

      // 如果是今天
      if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
        return date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      }
      
      // 如果是昨天
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)
      if (date.getDate() === yesterday.getDate()) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      }
      
      // 其他情况显示完整日期
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      console.warn('时间格式化失败:', error)
      return timestamp
    }
  }

  return {
    processMessageContentWithCodeBlocks,
    processLatexContent,
    processMarkdownContent,
    processMessage,
    formatTime
  }
}
