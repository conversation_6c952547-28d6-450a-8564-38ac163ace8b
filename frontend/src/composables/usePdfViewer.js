import { ref, computed, nextTick } from 'vue'

export function usePdfViewer() {
  // 侧边栏状态
  const sidebarVisible = ref(false)
  const uploadProgress = ref(0)
  const exportProgress = ref(0)
  const isExporting = ref(false)
  
  // 滚动和可见范围
  const scrollContainer = ref(null)
  const visibleRange = ref({ start: 1, end: 10 }) // 页面编号从1开始
  const itemHeight = ref(200) // 每个缩略图项的估计高度
  
  // 文件信息
  const currentFile = ref(null)
  
  // 计算属性
  const hasFile = computed(() => !!currentFile.value)
  const totalPages = computed(() => currentFile.value?.totalPages || 0)
  const selectedCount = computed(() => currentFile.value?.selectedPages?.size || 0)
  
  // 打开PDF侧边栏
  const openPdfSidebar = async (pdfInfo) => {
    currentFile.value = pdfInfo
    sidebarVisible.value = true
    // 重置导出状态与进度，避免残留状态导致误显示
    isExporting.value = false
    exportProgress.value = 0
    uploadProgress.value = 0

    // 重置可见范围为首屏
    visibleRange.value = { start: 1, end: Math.min(10, pdfInfo.totalPages || 10) }

    console.log('📱 打开PDF侧边栏:', {
      pdfInfo,
      isExporting: isExporting.value,
      exportProgress: exportProgress.value,
      visibleRange: visibleRange.value
    })

    // 等待DOM更新后初始化滚动
    await nextTick()
    initializeScrolling()
  }
  
  // 关闭PDF侧边栏（仅隐藏，保留文件状态）
  const closePdfSidebar = () => {
    sidebarVisible.value = false
    // 不清空 currentFile，保留PDF文件信息以便重新打开
    // currentFile.value = null
    uploadProgress.value = 0
    exportProgress.value = 0
    isExporting.value = false
    visibleRange.value = { start: 1, end: 10 } // 页面编号从1开始
    console.log('🗑️ PDF侧边栏已隐藏，文件状态保留')
  }

  // 清空PDF文件（彻底清理）
  const clearPdfFile = () => {
    sidebarVisible.value = false
    currentFile.value = null
    uploadProgress.value = 0
    exportProgress.value = 0
    isExporting.value = false
    visibleRange.value = { start: 1, end: 10 } // 页面编号从1开始
    console.log('🗑️ PDF文件已清空')
  }
  
  // 初始化滚动监听
  const initializeScrolling = () => {
    if (!scrollContainer.value) return
    
    // 初始计算可见范围
    updateVisibleRange()
  }
  
  // 更新可见范围（懒加载控制）
  const updateVisibleRange = () => {
    if (!scrollContainer.value || !totalPages.value) return
    
    const container = scrollContainer.value
    const scrollTop = container.scrollTop
    const containerHeight = container.clientHeight
    
    // 计算可见范围
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight.value) - 2)
    const endIndex = Math.min(
      totalPages.value - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight.value) + 2
    )
    
    const newRange = {
      start: startIndex + 1, // 页码从1开始
      end: endIndex + 1
    }
    
    // 只有范围变化时才更新
    if (newRange.start !== visibleRange.value.start || newRange.end !== visibleRange.value.end) {
      visibleRange.value = newRange
      console.log('📱 可见范围更新:', newRange)
    }
  }
  
  // 滚动事件处理（节流）
  let scrollTimer = null
  const handleScroll = () => {
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    
    scrollTimer = setTimeout(() => {
      updateVisibleRange()
    }, 100) // 100ms节流
  }
  
  // 滚动到指定页面
  const scrollToPage = (pageNum) => {
    if (!scrollContainer.value) return
    
    const targetScrollTop = (pageNum - 1) * itemHeight.value
    scrollContainer.value.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    })
  }
  
  // 设置上传进度
  const setUploadProgress = (progress) => {
    uploadProgress.value = Math.max(0, Math.min(100, progress))
  }
  
  // 设置导出进度
  const setExportProgress = (current, total) => {
    if (total > 0) {
      exportProgress.value = Math.round((current / total) * 100)
    } else {
      exportProgress.value = 0
    }
  }
  
  // 开始导出
  const startExport = () => {
    isExporting.value = true
    exportProgress.value = 0
  }
  
  // 完成导出
  const finishExport = () => {
    isExporting.value = false
    exportProgress.value = 100
    
    // 2秒后重置进度
    setTimeout(() => {
      exportProgress.value = 0
    }, 2000)
  }
  
  // 获取可见页面列表
  const getVisiblePages = (allPages) => {
    if (!allPages || allPages.length === 0) return []
    
    return allPages.filter(page => 
      page.num >= visibleRange.value.start && 
      page.num <= visibleRange.value.end
    )
  }
  
  // 预加载相邻页面
  const getPreloadPages = (allPages) => {
    if (!allPages || allPages.length === 0) return []
    
    const buffer = 3 // 预加载缓冲区
    const start = Math.max(1, visibleRange.value.start - buffer)
    const end = Math.min(totalPages.value, visibleRange.value.end + buffer)
    
    return allPages.filter(page => 
      page.num >= start && 
      page.num <= end &&
      !page.rendered &&
      !page.loading
    )
  }
  
  // 键盘快捷键处理
  const handleKeydown = (event) => {
    if (!sidebarVisible.value) return
    
    switch (event.key) {
      case 'Escape':
        closePdfSidebar()
        break
      case 'a':
      case 'A':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault()
          // 触发全选（需要从外部传入回调）
        }
        break
      case 'ArrowUp':
        if (event.ctrlKey) {
          event.preventDefault()
          scrollToPage(1)
        }
        break
      case 'ArrowDown':
        if (event.ctrlKey) {
          event.preventDefault()
          scrollToPage(totalPages.value)
        }
        break
    }
  }
  
  // 窗口大小变化处理
  const handleResize = () => {
    if (sidebarVisible.value) {
      updateVisibleRange()
    }
  }
  
  return {
    // 状态
    sidebarVisible,
    uploadProgress,
    exportProgress,
    isExporting,
    visibleRange,
    scrollContainer,
    itemHeight,
    currentFile,
    
    // 计算属性
    hasFile,
    totalPages,
    selectedCount,
    
    // 方法
    openPdfSidebar,
    closePdfSidebar,
    clearPdfFile,
    updateVisibleRange,
    handleScroll,
    scrollToPage,
    setUploadProgress,
    setExportProgress,
    startExport,
    finishExport,
    getVisiblePages,
    getPreloadPages,
    handleKeydown,
    handleResize
  }
}
