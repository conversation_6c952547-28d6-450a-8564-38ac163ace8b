<template>
  <div class="pdf-upload-button">
    <el-upload
      ref="uploadRef"
      accept="application/pdf"
      :show-file-list="false"
      :auto-upload="false"
      :before-upload="handleBeforeUpload"
      @change="handleFileChange"
    >
      <el-button
        type="default"
        :icon="Document"
        :loading="loading"
        :disabled="disabled"
      >
        {{ loading ? '处理中...' : '上传PDF' }}
      </el-button>
    </el-upload>
    
    <!-- 文件信息提示 -->
    <div v-if="currentFile" class="file-info">
      <el-icon class="file-icon"><Document /></el-icon>
      <span class="file-name">{{ currentFile.name }}</span>
      <span class="file-size">({{ formatFileSize(currentFile.size) }})</span>
      <el-button
        type="link"
        size="small"
        @click="clearFile"
      >
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Document, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  maxSize: {
    type: Number,
    default: 50 * 1024 * 1024 // 50MB
  }
})

const emit = defineEmits(['file-selected', 'file-cleared'])

const uploadRef = ref()
const currentFile = ref(null)

// 文件选择前的验证
const handleBeforeUpload = (file) => {
  console.log('📄 PDF文件选择:', file.name, file.size, file.type)
  
  // 检查文件类型
  if (file.type !== 'application/pdf') {
    ElMessage.error('只支持PDF文件格式')
    return false
  }
  
  // 检查文件大小
  if (file.size > props.maxSize) {
    const maxSizeMB = Math.round(props.maxSize / 1024 / 1024)
    ElMessage.error(`文件大小不能超过${maxSizeMB}MB`)
    return false
  }
  
  // 检查文件名
  if (!file.name || file.name.length > 255) {
    ElMessage.error('文件名无效或过长')
    return false
  }
  
  return true
}

// 文件选择处理
const handleFileChange = (uploadFile) => {
  const file = uploadFile.raw || uploadFile
  
  if (!handleBeforeUpload(file)) {
    return
  }
  
  currentFile.value = file
  emit('file-selected', file)
  
  console.log('✅ PDF文件选择成功:', file.name)
}

// 清除文件
const clearFile = () => {
  currentFile.value = null
  
  // 清除upload组件的文件列表
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  
  emit('file-cleared')
  console.log('🗑️ PDF文件已清除')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 暴露方法给父组件
defineExpose({
  clearFile
})
</script>

<style scoped>
.pdf-upload-button {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-color-light);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-small);
}

.file-icon {
  color: var(--color-primary);
  font-size: 16px;
}

.file-name {
  color: var(--text-primary);
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: var(--text-secondary);
  font-size: var(--font-size-small);
}

/* 上传按钮样式 */
:deep(.el-upload) {
  display: block;
}

:deep(.el-button) {
  width: 100%;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-name {
    max-width: 150px;
  }
}
</style>
