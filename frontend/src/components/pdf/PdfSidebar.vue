<template>
  <!-- 右侧侧边栏作为父容器的子元素，不使用 teleport -->
  <div class="pdf-sidebar-container">
    <div class="pdf-sidebar-content">
      <!-- 头部：标题栏 + 关闭按钮 -->
      <div class="pdf-header">
        <div class="header-title">
          <el-icon class="title-icon"><Document /></el-icon>
          <span class="title-text">PDF文档预览</span>
        </div>
        <el-button
          type="link"
          size="small"
          @click="handleClose"
          class="close-button"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>

      <!-- PDF切换器（多PDF时显示） -->
      <div v-if="pdfList.length > 1" class="pdf-switcher">
        <el-select
          :model-value="pdfInfo?.id"
          @change="handleSwitchPdf"
          placeholder="选择PDF文件"
          size="small"
          style="width: 100%"
        >
          <el-option
            v-for="pdf in pdfList"
            :key="pdf.id"
            :label="pdf.name"
            :value="pdf.id"
          >
            <span>{{ pdf.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 12px;">{{ pdf.totalPages }}页</span>
          </el-option>
        </el-select>
      </div>

      <!-- 文件信息 -->
      <div class="file-info" v-if="hasFile">
        <div class="file-details">
          <el-icon class="file-icon"><Document /></el-icon>
          <div class="file-text">
            <div class="filename" :title="filename">{{ filename }}</div>
            <div class="file-meta">
              <span class="page-count">共 {{ totalPages }} 页</span>
              <span class="file-size">{{ formatFileSize(fileSize) }}</span>
            </div>
          </div>
        </div>

        <div class="header-actions">
          <el-button-group size="small" class="action-group">
            <el-button @click="selectAll" :disabled="!hasPages" class="action-btn">
              <el-icon><Select /></el-icon>
              <span class="btn-text">全选</span>
            </el-button>
            <el-button @click="selectNone" :disabled="selectedCount === 0" class="action-btn">
              <el-icon><Close /></el-icon>
              <span class="btn-text">清空</span>
            </el-button>
            <el-button
              @click="showRangeSelector = !showRangeSelector"
              :disabled="!hasPages"
              :type="showRangeSelector ? 'primary' : ''"
              class="action-btn"
            >
              <el-icon><Grid /></el-icon>
              <span class="btn-text">范围</span>
            </el-button>
          </el-button-group>
        </div>

        <!-- 页码范围选择器 -->
        <div v-if="showRangeSelector && hasPages" class="range-selector">
          <div class="range-label">选择页码范围：</div>
          <div class="range-inputs">
            <div class="range-input-group">
              <label class="input-label">起始页</label>
              <el-input-number
                v-model="rangeStart"
                :min="1"
                :max="totalPages"
                size="small"
                :controls="false"
                class="range-input"
              />
            </div>
            <span class="range-separator">至</span>
            <div class="range-input-group">
              <label class="input-label">结束页</label>
              <el-input-number
                v-model="rangeEnd"
                :min="1"
                :max="totalPages"
                size="small"
                :controls="false"
                class="range-input"
              />
            </div>
            <el-button
              @click="handleRangeSelect"
              size="small"
              type="primary"
              :disabled="!isValidRange"
              class="range-button"
            >
              <el-icon><Select /></el-icon>
              选择
            </el-button>
          </div>
          <div v-if="!isValidRange" class="range-error">
            请输入有效的页码范围（1-{{ totalPages }}）
          </div>
        </div>

        <!-- 选择状态 -->
        <div v-if="selectedCount > 0" class="selection-status">
          <el-tag type="primary" size="small">
            已选择 {{ selectedCount }} 页
          </el-tag>
        </div>
      </div>

    <!-- 中部：缩略图列表 -->
    <div 
      ref="scrollContainer"
      class="pdf-pages"
      @scroll="handleScroll"
    >
      <div class="pages-container">
        <!-- 虚拟占位符（上方） -->
        <div 
          v-if="visibleRange.start > 1"
          class="virtual-spacer"
          :style="{ height: `${(visibleRange.start - 1) * itemHeight}px` }"
        />
        
        <!-- 可见页面 -->
        <PdfPageThumbnail
          v-for="page in visiblePages"
          :key="page.num"
          :page="page"
          :selected="selectedPages.has(page.num)"
          @toggle="togglePage"
          @load-request="handleLoadRequest"
        />
        
        <!-- 虚拟占位符（下方） -->
        <div 
          v-if="visibleRange.end < totalPages"
          class="virtual-spacer"
          :style="{ height: `${(totalPages - visibleRange.end) * itemHeight}px` }"
        />
      </div>
      
      <!-- 空状态 -->
      <div v-if="!hasPages && !isLoading" class="empty-state">
        <el-icon class="empty-icon"><Document /></el-icon>
        <p class="empty-text">暂无PDF页面</p>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-state">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p class="loading-text">正在加载PDF...</p>
      </div>
    </div>

    <!-- 底部：操作按钮 -->
    <div class="pdf-footer">
      <!-- 导出进度 -->
      <div v-if="isExporting" class="export-progress">
        <el-progress
          :percentage="safeExportProgress"
          :stroke-width="6"
          :text-inside="true"
          status="success"
        />
        <span class="progress-text">正在导出图片...</span>
      </div>

      <!-- 操作按钮 -->
      <div class="footer-actions">
        <el-button
          type="primary"
          size="large"
          :disabled="selectedCount === 0"
          :loading="isExporting"
          @click="handleAddToChat"
        >
          <el-icon><Plus /></el-icon>
          添加到聊天 ({{ selectedCount }})
        </el-button>
      </div>
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { Document, Select, Close, Plus, Loading, Grid } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import PdfPageThumbnail from './PdfPageThumbnail.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  pdfInfo: {
    type: Object,
    default: null
  },
  pages: {
    type: Array,
    default: () => []
  },
  selectedPages: {
    type: Set,
    default: () => new Set()
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  isExporting: {
    type: Boolean,
    default: false
  },
  exportProgress: {
    type: Number,
    default: 0
  },
  visibleRange: {
    type: Object,
    default: () => ({ start: 0, end: 10 })
  },
  pdfList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'update:visible',
  'close',
  'select-all',
  'select-none',
  'toggle-page',
  'add-to-chat',
  'scroll',
  'load-request',
  'ready',
  'switch-pdf',
  'range-select'
])

// 引用
const scrollContainer = ref(null)

// 页码范围选择
const showRangeSelector = ref(false)
const rangeStart = ref(1)
const rangeEnd = ref(1)

// 计算属性
const filename = computed(() => props.pdfInfo?.name || '未知文件')
const totalPages = computed(() => props.pdfInfo?.totalPages || 0)
const fileSize = computed(() => props.pdfInfo?.size || 0)
const selectedCount = computed(() => props.selectedPages?.size || 0)
const hasPages = computed(() => props.pages && props.pages.length > 0)
const hasFile = computed(() => !!props.pdfInfo && !!props.pdfInfo.name)
const itemHeight = computed(() => 240) // 每个缩略图项的高度

// 确保 exportProgress 是数字
const safeExportProgress = computed(() => {
  const progress = props.exportProgress
  if (typeof progress === 'number') return progress
  if (typeof progress === 'string') return parseFloat(progress) || 0
  return 0
})

// 页码范围验证
const isValidRange = computed(() => {
  return rangeStart.value >= 1 &&
         rangeEnd.value >= 1 &&
         rangeStart.value <= totalPages.value &&
         rangeEnd.value <= totalPages.value &&
         rangeStart.value <= rangeEnd.value
})

// 可见页面
const visiblePages = computed(() => {
  if (!hasPages.value) {
    return []
  }

  return props.pages.filter(page =>
    page.num >= props.visibleRange.start &&
    page.num <= props.visibleRange.end
  )
})

// 事件处理
const handleClose = () => {
  // 直接更新可见状态
  emit('update:visible', false)
  emit('close')
}

const selectAll = () => {
  emit('select-all')
}

const selectNone = () => {
  emit('select-none')
}

// PDF切换
const handleSwitchPdf = (pdfId) => {
  emit('switch-pdf', pdfId)
}

// 页码范围选择
const handleRangeSelect = () => {
  if (isValidRange.value) {
    emit('range-select', rangeStart.value, rangeEnd.value)
    showRangeSelector.value = false
  }
}

const togglePage = (pageNum) => {
  emit('toggle-page', pageNum)
}

const handleAddToChat = () => {
  if (selectedCount.value === 0) {
    ElMessage.warning('请先选择要添加的页面')
    return
  }
  
  emit('add-to-chat')
}

const handleScroll = () => {
  emit('scroll')
}

const handleLoadRequest = (pageNum) => {
  emit('load-request', pageNum)
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 键盘事件处理
const handleKeydown = (event) => {
  if (!props.visible) return
  
  switch (event.key) {
    case 'Escape':
      handleClose()
      break
    case 'a':
    case 'A':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        selectAll()
      }
      break
  }
}

// 监听可见性变化
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    await nextTick()
    // 初始化滚动容器
    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = 0
      // 通知父组件：滚动容器已准备好
      emit('ready', scrollContainer.value)
    }
  }
})

// 生命周期
onMounted(async () => {
  document.addEventListener('keydown', handleKeydown)

  // 组件初次挂载且可见时，通知父组件滚动容器已就绪
  if (props.visible) {
    await nextTick()
    if (scrollContainer.value) {
      emit('ready', scrollContainer.value)
    }
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* 右侧侧边栏：作为父容器子元素参与布局，不遮挡聊天区域 */
.pdf-sidebar-container {
  width: 420px;
  height: 100%;
  background: var(--bg-color);
  border-left: 1px solid var(--border-light);
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  flex: 0 0 420px;
}





.pdf-sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.pdf-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-color-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.title-icon {
  font-size: 18px;
  color: var(--color-primary);
}

.title-text {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
}

.close-button {
  padding: 4px;
  color: var(--text-secondary);
}

.close-button:hover {
  color: var(--text-primary);
  background: var(--bg-color-hover);
}

.file-info {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-color);
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.file-details {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  flex: 1;
  min-width: 0;
}

.file-icon {
  font-size: 24px;
  color: var(--color-primary);
  margin-top: 2px;
}

.file-text {
  flex: 1;
  min-width: 0;
}

.filename {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px;
}

.file-meta {
  display: flex;
  gap: var(--spacing-sm);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.header-actions {
  flex-shrink: 0;
}

.selection-status {
  text-align: center;
}

.pdf-pages {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
  background: var(--bg-color);
}

.pages-container {
  min-height: 100%;
}

.virtual-spacer {
  width: 100%;
  flex-shrink: 0;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-secondary);
}

.empty-icon,
.loading-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-sm);
}

.loading-icon {
  animation: rotate 1s linear infinite;
}

.empty-text,
.loading-text {
  font-size: var(--font-size-base);
}

.pdf-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  background: var(--bg-color);
  flex-shrink: 0;
}

.export-progress {
  margin-bottom: var(--spacing-md);
}

.progress-text {
  display: block;
  text-align: center;
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.footer-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.footer-actions .el-button {
  flex: 1;
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
.pdf-pages::-webkit-scrollbar {
  width: 6px;
}

.pdf-pages::-webkit-scrollbar-track {
  background: var(--bg-color-lighter);
}

.pdf-pages::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.pdf-pages::-webkit-scrollbar-thumb:hover {
  background: var(--border-color-hover);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-sidebar-container {
    width: 100vw;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .pdf-sidebar-container {
    background: var(--bg-color-dark);
    border-left-color: var(--border-color-dark);
  }
}

/* PDF切换器 */
.pdf-switcher {
  margin-bottom: var(--spacing-sm);
  padding: 0 var(--spacing-sm);
}

/* 页码范围选择器 */
.range-selector {
  margin-top: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.range-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
}

.range-inputs {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.range-input-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.input-label {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
}

.range-input {
  width: 70px !important;
}

.range-input :deep(.el-input__inner) {
  text-align: center;
  font-size: 12px;
}

.range-separator {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 12px;
  margin: 0 2px;
  align-self: center;
}

.range-button {
  margin-left: auto;
  min-width: 60px;
}

.range-error {
  margin-top: var(--spacing-xs);
  font-size: 11px;
  color: var(--color-danger);
  text-align: center;
}

.pdf-count {
  color: var(--text-secondary);
  font-size: 0.9em;
}

/* 操作按钮组优化 */
.header-actions {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xs);
}

.action-group {
  width: 100%;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  min-height: 28px;
}

.btn-text {
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .range-inputs {
    flex-direction: column;
    align-items: stretch;
  }

  .range-input-group {
    width: 100%;
  }

  .range-input {
    width: 100% !important;
  }

  .range-separator {
    align-self: center;
    margin: var(--spacing-xs) 0;
  }

  .range-button {
    margin-left: 0;
    width: 100%;
  }
}
</style>
