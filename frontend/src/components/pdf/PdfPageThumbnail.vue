<template>
  <div 
    class="page-thumbnail" 
    :class="{ 
      selected, 
      loading: page.loading,
      rendered: page.rendered 
    }"
  >
    <!-- 选择框 -->
    <div class="checkbox-container">
      <el-checkbox
        :model-value="selected"
        @change="handleToggle"
        :disabled="page.loading"
      />
    </div>
    
    <!-- 缩略图容器 -->
    <div 
      class="thumbnail-container"
      @click="handleToggle"
    >
      <!-- 已渲染的缩略图 -->
      <img
        v-if="page.rendered && page.thumbnailUrl"
        :src="page.thumbnailUrl"
        :alt="`第${page.num}页`"
        class="thumbnail-image"
        @load="handleImageLoad"
        @error="handleImageError"
      />

      <!-- 加载中占位符 -->
      <div v-else-if="page.loading" class="thumbnail-placeholder loading">
        <el-icon class="loading-icon">
          <Loading />
        </el-icon>
        <span class="loading-text">渲染中...</span>
      </div>
      
      <!-- 未渲染占位符 -->
      <div v-else class="thumbnail-placeholder">
        <el-icon class="placeholder-icon">
          <Document />
        </el-icon>
        <span class="placeholder-text">点击加载</span>
      </div>
      
      <!-- 选中状态遮罩 -->
      <div v-if="selected" class="selection-overlay">
        <el-icon class="check-icon">
          <Check />
        </el-icon>
      </div>
    </div>
    
    <!-- 页码信息 -->
    <div class="page-info">
      <span class="page-number">第 {{ page.num }} 页</span>
      <span v-if="page.rendered" class="page-status">✓</span>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { Loading, Document, Check } from '@element-plus/icons-vue'

const props = defineProps({
  page: {
    type: Object,
    required: true,
    validator: (page) => {
      return page && 
             typeof page.num === 'number' && 
             typeof page.rendered === 'boolean' &&
             typeof page.loading === 'boolean'
    }
  },
  selected: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['toggle', 'load-request'])

// 计算属性
const isClickable = computed(() => !props.page.loading)

// 处理选择切换
const handleToggle = () => {
  if (!isClickable.value) return
  
  emit('toggle', props.page.num)
  console.log(`📄 页面选择切换: 第${props.page.num}页, 选中: ${!props.selected}`)
}

// 处理图片加载完成
const handleImageLoad = () => {
  console.log(`🖼️ 缩略图加载完成: 第${props.page.num}页`)
}

// 处理图片加载错误
const handleImageError = () => {
  console.error(`❌ 缩略图加载失败: 第${props.page.num}页`)
}

// 请求加载缩略图（当组件进入可见区域时）
const requestLoad = () => {
  if (!props.page.rendered && !props.page.loading) {
    emit('load-request', props.page.num)
  }
}

// 组件挂载时自动请求加载缩略图
onMounted(() => {
  // 如果缩略图还没有渲染且不在加载中，自动请求加载
  if (!props.page.rendered && !props.page.loading) {
    requestLoad()
  }
})

// 暴露方法给父组件
defineExpose({
  requestLoad
})
</script>

<style scoped>
.page-thumbnail {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  border: 2px solid transparent;
  border-radius: var(--border-radius-base);
  background: var(--bg-color);
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
}

.page-thumbnail:hover {
  border-color: var(--border-color-hover);
  background: var(--bg-color-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.page-thumbnail.selected {
  border-color: var(--color-primary);
  background: rgba(var(--color-primary-rgb), 0.05);
}

.page-thumbnail.loading {
  cursor: wait;
}

.checkbox-container {
  align-self: flex-start;
  margin-bottom: var(--spacing-xs);
}

.thumbnail-container {
  position: relative;
  width: 160px;
  height: 200px;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-small);
  overflow: hidden;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.page-thumbnail:hover .thumbnail-image {
  transform: scale(1.02);
}

.thumbnail-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--text-secondary);
  background: var(--bg-color-lighter);
}

.thumbnail-placeholder.loading {
  color: var(--color-primary);
}

.loading-icon,
.placeholder-icon {
  font-size: 32px;
  margin-bottom: var(--spacing-xs);
}

.loading-icon {
  animation: rotate 1s linear infinite;
}

.loading-text,
.placeholder-text {
  font-size: var(--font-size-small);
  font-weight: 500;
}

.selection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-primary-rgb), 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(1px);
}

.check-icon {
  font-size: 24px;
  color: var(--color-primary);
  background: white;
  border-radius: 50%;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.page-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: var(--spacing-xs);
  padding: 0 var(--spacing-xs);
}

.page-number {
  font-size: var(--font-size-small);
  font-weight: 500;
  color: var(--text-primary);
}

.page-status {
  font-size: var(--font-size-small);
  color: var(--color-success);
  font-weight: bold;
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .thumbnail-container {
    width: 140px;
    height: 180px;
  }
  
  .page-thumbnail {
    padding: var(--spacing-xs);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .thumbnail-container {
    background: var(--bg-color-darker);
    border-color: var(--border-color-dark);
  }
  
  .thumbnail-placeholder {
    background: var(--bg-color-dark);
  }
}
</style>
