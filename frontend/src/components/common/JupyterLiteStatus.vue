<template>
  <div v-if="showStatus" class="jupyter-lite-status">
    <div class="status-content">
      <div class="status-icon">
        <el-icon v-if="status.isLoading" class="loading-icon">
          <Loading />
        </el-icon>
        <el-icon v-else-if="status.isLoaded" class="success-icon">
          <Check />
        </el-icon>
        <el-icon v-else class="idle-icon">
          <VideoPlay />
        </el-icon>
      </div>
      
      <div class="status-info">
        <div class="status-title">
          {{ statusTitle }}
        </div>
        <div v-if="status.isLoading" class="status-details">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: status.progress + '%' }"
            ></div>
          </div>
          <div class="progress-text">
            {{ status.stage }} ({{ status.progress }}%)
          </div>
        </div>
      </div>
      
      <div class="status-actions">
        <el-button 
          v-if="!status.isLoaded && !status.isLoading"
          size="small" 
          type="primary"
          @click="$emit('start-loading')"
        >
          开始加载
        </el-button>
        <el-button 
          v-if="status.isLoaded"
          size="small" 
          type="success"
          disabled
        >
          已就绪
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Loading, Check, VideoPlay } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  status: {
    type: Object,
    required: true,
    default: () => ({
      isLoading: false,
      isLoaded: false,
      progress: 0,
      stage: '',
      canExecute: false
    })
  },
  showWhenIdle: {
    type: Boolean,
    default: false
  }
})

// Emits
defineEmits(['start-loading'])

// 计算属性
const showStatus = computed(() => {
  return props.status.isLoading || props.status.isLoaded || props.showWhenIdle
})

const statusTitle = computed(() => {
  if (props.status.isLoading) {
    return 'Python执行环境加载中'
  } else if (props.status.isLoaded) {
    return 'Python执行环境已就绪'
  } else {
    return 'Python执行环境未加载'
  }
})
</script>

<style scoped>
.jupyter-lite-status {
  background: var(--bg-color);
  border: 1px solid var(--border-light);
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  transition: var(--transition-base);
}

.jupyter-lite-status:hover {
  box-shadow: var(--box-shadow-light);
}

.status-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  flex-shrink: 0;
}

.loading-icon {
  color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.success-icon {
  color: var(--success-color);
}

.idle-icon {
  color: var(--info-color);
}

.status-info {
  flex: 1;
  min-width: 0;
}

.status-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--border-lighter);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.status-actions {
  flex-shrink: 0;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 深色模式适配 */
.dark .jupyter-lite-status {
  background: var(--bg-color);
  border-color: var(--border-base);
}

.dark .progress-bar {
  background: var(--border-base);
}
</style>
