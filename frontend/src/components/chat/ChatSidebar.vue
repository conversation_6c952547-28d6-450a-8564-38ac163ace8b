<template>
  <div class="sidebar" :class="{ collapsed: collapsed }">
    <div class="sidebar-header">
      <h2 v-if="!collapsed">InspirFlow</h2>
      <el-button
        :icon="collapsed ? Expand : Fold"
        circle
        size="small"
        @click="$emit('toggle-sidebar')"
      />
    </div>

    <!-- 对话列表 -->
    <div class="conversations-section">
      <div class="section-title" v-if="!collapsed">
        <span>对话列表</span>
        <el-button :icon="Plus" size="small" @click="$emit('create-conversation')">
          新建对话
        </el-button>
      </div>
      <div class="conversations-list">
        <div
          v-for="conv in conversations"
          :key="conv.id"
          class="conversation-item"
          :class="{ active: currentConversationId === conv.id }"
        >
          <div class="conversation-content" @click="$emit('select-conversation', conv.id)">
            <el-icon><ChatDotRound /></el-icon>
            <span v-if="!collapsed" class="conversation-title">
              {{ conv.title || '新对话' }}
            </span>
          </div>
          <el-button
            v-if="!collapsed"
            :icon="Delete"
            size="small"
            type="danger"
            text
            class="delete-btn"
            @click.stop="$emit('delete-conversation', conv.id)"
          />
        </div>
      </div>
    </div>

    <!-- 模型选择 -->
    <div class="model-section" v-if="!collapsed">
      <div class="section-title">
        <span>当前模型</span>
      </div>
      <el-select
        :model-value="selectedModel"
        @update:model-value="$emit('update:selectedModel', $event)"
        placeholder="选择模型"
        style="width: 100%"
        size="small"
      >
        <el-option
          v-for="model in models"
          :key="model.id"
          :label="model.name"
          :value="model.id"
        />
      </el-select>
    </div>

    <!-- 系统设置 -->
    <div class="settings-section" v-if="!collapsed">
      <el-button
        type="primary"
        :icon="Setting"
        @click="$emit('open-settings')"
        class="settings-button"
        size="small"
      >
        系统设置
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ChatDotRound, Plus, Expand, Fold, Delete, Setting } from '@element-plus/icons-vue'

defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  conversations: {
    type: Array,
    default: () => []
  },
  currentConversationId: {
    type: Number,
    default: null
  },
  models: {
    type: Array,
    default: () => []
  },
  selectedModel: {
    type: String,
    default: null
  }
})

defineEmits([
  'toggle-sidebar',
  'create-conversation',
  'select-conversation',
  'delete-conversation',
  'open-settings',
  'update:selectedModel'
])
</script>

<style scoped>
.sidebar {
  width: var(--sidebar-width, 300px);
  background: var(--bg-color);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  transition: var(--transition-base);
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width, 60px);
}

.sidebar-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header h2 {
  font-size: var(--font-size-large);
  color: var(--text-primary);
  margin: 0;
}

.conversations-section {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-small);
}

.conversations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  transition: var(--transition-base);
}

.conversation-item:hover {
  background: var(--bg-color-light);
}

.conversation-item.active {
  background: var(--primary-color-light);
  color: var(--primary-color);
}

.conversation-title {
  font-size: var(--font-size-small);
}

.temperature-setting label,
.latex-setting .setting-row label {
  font-size: var(--font-size-small);
  color: var(--text-primary);
}


.conversation-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  min-width: 0;
}

.conversation-title {
  margin-left: var(--spacing-sm);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: var(--font-size-base);
}

.model-section, .settings-section {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.model-section .section-title {
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.settings-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.settings-button:active {
  transform: translateY(0);
}

.delete-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-item:hover .delete-btn {
  opacity: 1;
}
</style>
