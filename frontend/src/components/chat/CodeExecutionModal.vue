<template>
  <el-dialog
    v-model="visible"
    title="Python代码执行"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    class="code-execution-modal"
    @close="handleClose"
  >
    <!-- 执行状态头部 -->
    <div class="execution-header">
      <div class="execution-info">
        <el-icon v-if="executing" class="executing-icon">
          <Loading />
        </el-icon>
        <el-icon v-else-if="executionResult" class="success-icon">
          <Check />
        </el-icon>
        <el-icon v-else class="ready-icon">
          <VideoPlay />
        </el-icon>
        <span class="execution-status">
          {{ executionStatusText }}
        </span>
        <span v-if="executionTime" class="execution-time">
          ({{ executionTime }}ms)
        </span>
      </div>
      <div class="execution-actions">
        <el-button
          v-if="executing"
          type="danger"
          size="small"
          @click="stopExecution"
        >
          停止执行
        </el-button>
        <el-button
          v-else
          type="primary"
          size="small"
          :disabled="!canExecute"
          @click="executeCode"
        >
          <el-icon><VideoPlay /></el-icon>
          运行代码
        </el-button>
      </div>
    </div>

    <!-- 代码预览 -->
    <div class="code-preview">
      <div class="code-header">
        <span class="code-language">PYTHON</span>
        <span class="code-lines">{{ codeLines }} 行</span>
      </div>
      <div class="code-content">
        <pre><code class="hljs language-python">{{ code }}</code></pre>
      </div>
    </div>

    <!-- 执行结果 -->
    <div v-if="executionResult || executing" class="execution-results">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 输出标签页 -->
        <el-tab-pane label="输出" name="output">
          <div class="output-content">
            <div v-if="executing" class="executing-placeholder">
              <el-icon class="rotating"><Loading /></el-icon>
              <span>代码执行中...</span>
            </div>
            <div v-else-if="executionResult?.stdout" class="stdout">
              <pre>{{ executionResult.stdout }}</pre>
            </div>
            <div v-else class="no-output">
              <el-icon><InfoFilled /></el-icon>
              <span>无输出内容</span>
            </div>
          </div>
        </el-tab-pane>

        <!-- 图像标签页 -->
        <el-tab-pane name="images">
          <template #label>
            <span>
              图像
              <el-badge
                v-if="executionResult?.images?.length"
                :value="executionResult.images.length"
                class="tab-badge"
              />
            </span>
          </template>
          <div class="images-content">
            <div v-if="executing" class="executing-placeholder">
              <el-icon class="rotating"><Loading /></el-icon>
              <span>生成图像中...</span>
            </div>
            <div v-else-if="executionResult?.images?.length" class="images-grid">
              <div
                v-for="(image, index) in executionResult.images"
                :key="index"
                class="image-item"
              >
                <div v-if="image && image.data && image.data.startsWith('data:image/')" class="valid-image">
                  <img
                    :src="image.data"
                    :alt="`图像 ${index + 1}`"
                    @click="previewImage(image.data, index)"
                    @error="handleImageError($event, index)"
                  />
                  <div class="image-actions">
                    <el-button size="small" @click="downloadImage(image.data, index)">
                      下载
                    </el-button>
                  </div>
                </div>
                <div v-else class="invalid-image">
                  <el-icon><Picture /></el-icon>
                  <span>图像数据无效</span>
                  <small>{{ image ? '数据格式错误' : '图像对象为空' }}</small>
                </div>
              </div>
            </div>
            <div v-else class="no-images">
              <el-icon><Picture /></el-icon>
              <span>无图像输出</span>
            </div>
          </div>
        </el-tab-pane>

        <!-- 错误标签页 -->
        <el-tab-pane name="errors">
          <template #label>
            <span>
              错误
              <el-badge
                v-if="executionResult?.stderr"
                is-dot
                class="tab-badge error-badge"
              />
            </span>
          </template>
          <div class="errors-content">
            <div v-if="executing" class="executing-placeholder">
              <el-icon class="rotating"><Loading /></el-icon>
              <span>检查错误中...</span>
            </div>
            <div v-else-if="executionResult?.stderr" class="stderr">
              <pre>{{ executionResult.stderr }}</pre>
            </div>
            <div v-else class="no-errors">
              <el-icon><SuccessFilled /></el-icon>
              <span>无错误</span>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 底部提示 -->
    <div class="execution-footer">
      <div class="execution-tips">
        <el-icon><InfoFilled /></el-icon>
        <span>代码在浏览器沙箱中执行，结果不会保存</span>
      </div>
    </div>

    <!-- 图像预览 -->
    <el-image-viewer
      v-if="imagePreviewVisible"
      :url-list="[imagePreviewUrl]"
      :initial-index="0"
      @close="imagePreviewVisible = false"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Loading,
  Check,
  VideoPlay,
  InfoFilled,
  Picture,
  SuccessFilled
} from '@element-plus/icons-vue'
import { useJupyterLite } from '@/composables/useJupyterLite'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  code: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// JupyterLite Hook
const { canExecuteCode, executePythonCode, getLoadingStatus } = useJupyterLite()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const executing = ref(false)
const executionResult = ref(null)
const executionTime = ref(null)
const activeTab = ref('output')
const imagePreviewVisible = ref(false)
const imagePreviewUrl = ref('')

// 计算属性
const codeLines = computed(() => {
  return props.code.split('\n').length
})

const canExecute = computed(() => {
  return canExecuteCode.value && !executing.value
})

const executionStatusText = computed(() => {
  if (executing.value) {
    return '执行中...'
  } else if (executionResult.value) {
    return '执行完成'
  } else {
    return '准备执行'
  }
})

// 监听弹窗打开，重置状态
watch(visible, (newVisible) => {
  if (newVisible) {
    resetExecutionState()
  }
})

// 方法
const resetExecutionState = () => {
  executing.value = false
  executionResult.value = null
  executionTime.value = null
  activeTab.value = 'output'
}

const executeCode = async () => {
  if (!canExecute.value) {
    ElMessage.warning('Python执行环境尚未准备就绪，请稍候再试')
    return
  }

  executing.value = true
  executionResult.value = null
  executionTime.value = null

  try {
    const startTime = Date.now()
    const result = await executePythonCode(props.code)
    const endTime = Date.now()

    executionResult.value = result
    executionTime.value = endTime - startTime

    // 如果有图像输出，自动切换到图像标签页
    if (result.images && result.images.length > 0) {
      console.log('🔄 切换到图像标签页，图片数量:', result.images.length)
      activeTab.value = 'images'
    }
    // 如果有错误或执行失败，自动切换到错误标签页
    else if (result.stderr || !result.success) {
      activeTab.value = 'errors'
    }

    // 根据执行结果显示不同消息
    if (result.success) {
      ElMessage.success('代码执行完成')
    } else {
      ElMessage.warning('代码执行完成，但有错误')
    }
  } catch (error) {
    console.error('代码执行失败:', error)
    ElMessage.error('代码执行失败: ' + error.message)
    
    // 显示错误信息
    executionResult.value = {
      stdout: '',
      stderr: error.message,
      images: [],
      execTimeMs: 0
    }
    activeTab.value = 'errors'
  } finally {
    executing.value = false
  }
}

const stopExecution = () => {
  executing.value = false
  ElMessage.info('已停止代码执行')
}

const previewImage = (imageData, index) => {
  if (!imageData || typeof imageData !== 'string') {
    console.error('图片数据无效:', imageData)
    ElMessage.error('图片数据无效')
    return
  }
  imagePreviewUrl.value = imageData
  imagePreviewVisible.value = true
}

const downloadImage = (imageData, index) => {
  try {
    if (!imageData || typeof imageData !== 'string') {
      console.error('图片数据无效:', imageData)
      ElMessage.error('图片数据无效，无法下载')
      return
    }

    // 验证是否为有效的data URL
    if (!imageData.startsWith('data:image/')) {
      console.error('不是有效的图片数据URL:', imageData.substring(0, 50))
      ElMessage.error('图片数据格式错误')
      return
    }

    const link = document.createElement('a')
    link.href = imageData
    link.download = `python_plot_${index + 1}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('图像下载成功')
  } catch (error) {
    console.error('图像下载失败:', error)
    ElMessage.error('图像下载失败: ' + error.message)
  }
}

const handleImageError = (event, index) => {
  console.error(`图片 ${index + 1} 加载失败:`, event)
  ElMessage.error(`图片 ${index + 1} 加载失败`)
}

const handleClose = () => {
  if (executing.value) {
    stopExecution()
  }
  visible.value = false
}
</script>

<style scoped>
.code-execution-modal :deep(.el-dialog) {
  border-radius: 8px;
}

.code-execution-modal :deep(.el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-light);
}

.code-execution-modal :deep(.el-dialog__body) {
  padding: 0;
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-color-light);
  border-bottom: 1px solid var(--border-light);
}

.execution-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.executing-icon {
  color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.success-icon {
  color: var(--success-color);
}

.ready-icon {
  color: var(--info-color);
}

.execution-status {
  font-weight: 500;
  color: var(--text-primary);
}

.execution-time {
  color: var(--text-secondary);
  font-size: 12px;
}

.code-preview {
  margin: 20px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  overflow: hidden;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--bg-color-light);
  border-bottom: 1px solid var(--border-light);
}

.code-language {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

.code-lines {
  font-size: 12px;
  color: var(--text-placeholder);
}

.code-content {
  max-height: 200px;
  overflow-y: auto;
}

.code-content pre {
  margin: 0;
  padding: 16px;
  background: transparent;
  font-size: 14px;
  line-height: 1.6;
}

.execution-results {
  margin: 20px;
}

.output-content,
.images-content,
.errors-content {
  min-height: 200px;
  padding: 16px;
}

.executing-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

.rotating {
  animation: spin 1s linear infinite;
}

.stdout,
.stderr {
  background: var(--bg-color-light);
  border-radius: 4px;
  padding: 12px;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.stderr {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.no-output,
.no-images,
.no-errors {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--text-placeholder);
  font-size: 14px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.image-item {
  border: 1px solid var(--border-light);
  border-radius: 6px;
  overflow: hidden;
  transition: var(--transition-base);
}

.image-item:hover {
  box-shadow: var(--box-shadow-light);
}

.image-item img {
  width: 100%;
  height: 150px;
  object-fit: contain;
  background: #f9f9f9;
  cursor: pointer;
}

.image-actions {
  padding: 8px;
  text-align: center;
  background: var(--bg-color-light);
}

.invalid-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  background: #f9f9f9;
  color: var(--text-placeholder);
  border: 1px dashed var(--border-light);
  border-radius: 4px;
}

.invalid-image .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.invalid-image small {
  font-size: 11px;
  margin-top: 4px;
  opacity: 0.7;
}

.execution-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-light);
  background: var(--bg-color-light);
}

.execution-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--text-secondary);
  font-size: 12px;
}

.tab-badge {
  margin-left: 4px;
}

.error-badge :deep(.el-badge__content) {
  background-color: var(--danger-color);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
