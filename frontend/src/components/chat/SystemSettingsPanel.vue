<template>
  <div class="settings-panel" :class="{ visible: visible }">
    <div class="settings-overlay" @click="$emit('close')"></div>
    <div class="settings-content">
      <div class="settings-header">
        <h3>系统设置</h3>
        <el-button :icon="Close" circle size="small" @click="$emit('close')" />
      </div>

      <div class="settings-body">
        <!-- 用户信息 -->
        <div class="setting-section">
          <h4>用户信息</h4>
          <div class="user-info">
            <div class="user-detail">
              <span class="label">API密钥:</span>
              <span class="value">{{ userInfo?.api_key || 'N/A' }}</span>
            </div>
            <div class="user-detail">
              <span class="label">余额:</span>
              <span class="value">{{ userInfo?.current_balance || '0' }}</span>
            </div>
            <div class="user-detail">
              <span class="label">总消费:</span>
              <span class="value">{{ userInfo?.total_spent || '0' }}</span>
            </div>
            <div class="user-actions">
              <el-button type="primary" @click="$emit('logout')">退出登录</el-button>
            </div>
          </div>
        </div>

        <!-- 温度设置 -->
        <div class="setting-section">
          <h4>温度设置</h4>
          <el-form label-width="100px" size="small">
            <el-form-item label="温度">
              <div class="temperature-control">
                <el-slider
                  :model-value="temperature"
                  @update:model-value="$emit('update:temperature', $event)"
                  :min="0"
                  :max="2"
                  :step="0.1"
                  show-input
                  :show-input-controls="false"
                  style="flex: 1; margin-right: 12px"
                />
                <span class="temperature-value">{{ temperature }}</span>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <!-- 渲染设置 -->
        <div class="setting-section">
          <h4>渲染设置</h4>
          <el-form label-width="100px" size="small">
            <el-form-item label="LaTeX渲染">
              <el-switch
                :model-value="latexRenderEnabled"
                @update:model-value="$emit('update:latexRenderEnabled', $event)"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 提示词设置 -->
        <div class="setting-section">
          <h4>系统提示词</h4>
          <div class="prompt-options">
            <el-checkbox-group
              :model-value="selectedPrompts"
              @update:model-value="$emit('update:selectedPrompts', $event)"
            >
              <!-- 按分类显示提示词 -->
              <div v-for="category in promptCategories" :key="category.id" class="prompt-category">
                <h5 class="category-title">{{ category.name }}</h5>
                <p class="category-description">{{ category.description }}</p>

                <div v-for="prompt in getPromptsByCategory(category.id)" :key="prompt.id" class="prompt-item">
                  <el-checkbox
                    :value="prompt.id"
                    :title="prompt.description"
                  >
                    <span class="prompt-title">{{ prompt.title }}</span>
                  </el-checkbox>
                </div>
              </div>
            </el-checkbox-group>
          </div>
        </div>

        <!-- 自定义提示词 -->
        <div class="setting-section">
          <h4>自定义提示词</h4>
          <el-input
            :model-value="customPrompt"
            @update:model-value="$emit('update:customPrompt', $event)"
            type="textarea"
            :rows="4"
            placeholder="输入自定义的系统提示词..."
            maxlength="1000"
            show-word-limit
          />
        </div>
      </div>

      <div class="settings-footer">
        <el-button @click="$emit('reset')">重置默认</el-button>
        <el-button type="primary" @click="$emit('save')">保存设置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>

import { Close } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userInfo: {
    type: Object,
    default: () => ({})
  },
  temperature: {
    type: Number,
    default: 0.8
  },
  latexRenderEnabled: {
    type: Boolean,
    default: true
  },
  selectedPrompts: {
    type: Array,
    default: () => []
  },
  customPrompt: {
    type: String,
    default: ''
  },
  availablePrompts: {
    type: Array,
    default: () => []
  },
  promptCategories: {
    type: Array,
    default: () => []
  }
})

defineEmits([
  'close',
  'update:temperature',
  'update:latexRenderEnabled',
  'update:selectedPrompts',
  'update:customPrompt',
  'save',
  'reset',
  'logout'
])

// 按分类获取提示词
const getPromptsByCategory = (categoryId) => {
  return props.availablePrompts.filter(prompt => prompt.category === categoryId)
}
</script>

<style scoped>
.settings-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
}

.settings-panel.visible {
  opacity: 1;
  visibility: visible;
}

.settings-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
}

.settings-content {
  position: relative;
  width: 90%;
  max-width: 700px;
  max-height: 85vh;
  background: var(--bg-color);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.settings-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.settings-body {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.setting-section {
  margin-bottom: var(--spacing-xl);
  padding: 20px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.setting-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid rgba(102, 126, 234, 0.3);
  padding-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: 8px;
}

.temperature-control {
  display: flex;
  align-items: center;
  width: 100%;
}

.temperature-value {
  font-size: var(--font-size-small);
  color: var(--text-regular);
  min-width: 40px;
  text-align: center;
}

.prompt-options {
  max-height: 400px;
  overflow-y: auto;
}

.prompt-category {
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.prompt-category:last-child {
  margin-bottom: 0;
}

.category-title {
  font-size: 1rem;
  font-weight: 600;
  color: #667eea;
  margin: 0 0 6px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.5;
  opacity: 0.8;
}

.prompt-item {
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.prompt-item:last-child {
  margin-bottom: 0;
}

.prompt-item .el-checkbox {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  transition: all 0.2s ease;
}

.prompt-item .el-checkbox:hover {
  border-color: rgba(102, 126, 234, 0.4);
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-1px);
}

.prompt-item .el-checkbox.is-checked {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.prompt-title {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.95rem;
  margin: 0;
  cursor: pointer;
}

.settings-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  background: var(--bg-color-light);
}

/* 滚动条样式 */
.settings-body::-webkit-scrollbar,
.prompt-options::-webkit-scrollbar {
  width: 6px;
}

.settings-body::-webkit-scrollbar-track,
.prompt-options::-webkit-scrollbar-track {
  background: var(--bg-color-light);
  border-radius: 3px;
}

.settings-body::-webkit-scrollbar-thumb,
.prompt-options::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.settings-body::-webkit-scrollbar-thumb:hover,
.prompt-options::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 用户信息样式 */
.user-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--bg-lighter);
  border-radius: 6px;
}

.user-detail .label {
  font-weight: 500;
  color: var(--text-primary);
}

.user-detail .value {
  color: var(--text-secondary);
  font-family: monospace;
}

.user-actions {
  margin-top: 8px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-content {
    width: 95%;
    max-height: 90vh;
  }
  
  .settings-header,
  .settings-body,
  .settings-footer {
    padding: var(--spacing-md);
  }
}
</style>
