<template>
  <div class="input-area">
    <el-input
      ref="textareaRef"
      :model-value="inputMessage"
      @update:model-value="$emit('update:inputMessage', $event)"
      type="textarea"
      :rows="5"
      placeholder="输入您的消息... (支持粘贴图片)"
      :disabled="isGenerating"
      @keydown.ctrl.enter="$emit('send-message')"
      @paste="handlePaste"
    />
    
    <!-- 待发送的图片预览区 -->
    <div v-if="pendingPreviews.length" class="pending-attachments">
      <div v-for="p in pendingPreviews" :key="p.id" class="pending-thumb">
        <img 
          :src="p.url" 
          :alt="p.name" 
          @click="$emit('open-image-viewer', p.url)" 
        />
        <span class="name">{{ p.name }}</span>
        <el-button 
          size="small" 
          text 
          type="danger" 
          @click="$emit('remove-pending-attachment', p.id)"
        >
          移除
        </el-button>
      </div>
    </div>
    
    <div class="input-actions">
      <!-- 上传进度条 -->
      <div v-if="uploadingImage" class="upload-progress">
        <el-progress
          :percentage="uploadProgress"
          :stroke-width="6"
          :text-inside="true"
          status="success"
        />
      </div>

      <!-- 余额显示 -->
      <div class="balance-info">
        <span class="balance-label">余额:</span>
        <span
          class="balance-amount"
          :class="{
            'balance-low': userBalance < 0.01 && userBalance >= 0,
            'balance-negative': userBalance < 0
          }"
        >
          ${{ userBalance.toFixed(6) }}
        </span>
      </div>

      <div class="action-buttons">
        <el-upload
          accept="image/*"
          :show-file-list="false"
          :auto-upload="false"
          @change="$emit('upload-change', $event)"
        >
          <el-button type="default">上传图片</el-button>
        </el-upload>

        <el-button
          type="default"
          :icon="Document"
          @click="$emit('upload-pdf')"
        >
          上传PDF
        </el-button>

        <el-button
          type="primary"
          :loading="isGenerating"
          :disabled="!inputMessage.trim() || userBalance < 0"
          @click="$emit('send-message')"
        >
          发送 (Ctrl+Enter)
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'

const authStore = useAuthStore()
const textareaRef = ref()

defineProps({
  inputMessage: {
    type: String,
    default: ''
  },
  isGenerating: {
    type: Boolean,
    default: false
  },
  pendingPreviews: {
    type: Array,
    default: () => []
  },
  uploadingImage: {
    type: Boolean,
    default: false
  },
  uploadProgress: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits([
  'update:inputMessage',
  'send-message',
  'upload-change',
  'open-image-viewer',
  'remove-pending-attachment',
  'upload-pdf'
])

// 处理粘贴事件
const handlePaste = async (event) => {
  const clipboardData = event.clipboardData || window.clipboardData
  if (!clipboardData) return

  console.log('📋 粘贴事件触发，剪贴板项目数量:', clipboardData.items?.length || 0)

  // 检查是否包含文字内容
  const hasText = clipboardData.types && (
    clipboardData.types.includes('text/plain') ||
    clipboardData.types.includes('text/html')
  )

  // 检查是否包含图片
  let hasImage = false
  if (clipboardData.items) {
    for (let i = 0; i < clipboardData.items.length; i++) {
      const item = clipboardData.items[i]
      if (item.type.indexOf('image') !== -1) {
        hasImage = true
        break
      }
    }
  }

  console.log('📋 内容分析 - 包含文字:', hasText, '包含图片:', hasImage)

  // 策略1: 如果只有图片，没有文字 → 上传图片
  if (hasImage && !hasText) {
    console.log('📋 策略1: 纯图片粘贴，开始上传处理')
    event.preventDefault()

    // 查找并处理图片
    const items = clipboardData.items
    for (let i = 0; i < items.length; i++) {
      const item = items[i]

      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile()
        if (file) {
          await processImageFile(file, '纯图片粘贴')
          return // 处理完第一张图片就返回
        }
      }
    }

    // 如果上面的方法没找到图片，尝试其他方法
    await tryAlternativeImageMethods(event, clipboardData)
    return
  }

  // 策略2: 如果既有文字又有图片 → 只保留文字，让浏览器默认处理
  if (hasText && hasImage) {
    console.log('📋 策略2: 混合内容粘贴，只保留文字')
    ElMessage.info('检测到混合内容，已提取文字内容（图片已忽略）')
    // 不阻止默认行为，让浏览器正常粘贴文字
    return
  }

  // 策略3: 如果只有文字 → 让浏览器默认处理
  if (hasText && !hasImage) {
    console.log('📋 策略3: 纯文字粘贴，使用默认处理')
    // 不阻止默认行为，让浏览器正常粘贴文字
    return
  }

  // 策略4: 其他情况，尝试图片处理
  console.log('📋 策略4: 其他情况，尝试图片处理')
  await tryAlternativeImageMethods(event, clipboardData)
}

// 尝试其他图片检测方法
const tryAlternativeImageMethods = async (event, clipboardData) => {
  const items = clipboardData.items
  if (!items) return

  // 方法1: 检查文件类型（可能是没有明确MIME类型的图片）
  for (let i = 0; i < items.length; i++) {
    const item = items[i]

    if (item.kind === 'file' && !item.type) {
      console.log('📋 检测到文件类型，尝试获取为图片')
      const file = item.getAsFile()
      if (file) {
        const isImage = await checkIfImageFile(file)
        if (isImage) {
          event.preventDefault()
          await processImageFile(file, 'Word等应用')
          return
        }
      }
    }
  }

  // 方法2: 检查clipboardData.files
  if (clipboardData.files && clipboardData.files.length > 0) {
    for (let i = 0; i < clipboardData.files.length; i++) {
      const file = clipboardData.files[i]
      if (file.type.startsWith('image/') || await checkIfImageFile(file)) {
        event.preventDefault()
        await processImageFile(file, 'clipboardData.files')
        return
      }
    }
  }

  // 方法3: 尝试Clipboard API
  if (navigator.clipboard && navigator.clipboard.read) {
    try {
      const clipboardItems = await navigator.clipboard.read()
      for (const clipboardItem of clipboardItems) {
        for (const type of clipboardItem.types) {
          if (type.startsWith('image/')) {
            event.preventDefault()
            const blob = await clipboardItem.getType(type)
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
            const extension = type.split('/')[1] || 'png'
            const fileName = `粘贴图片-${timestamp}.${extension}`
            const file = new File([blob], fileName, { type })
            await processImageFile(file, 'Clipboard API')
            return
          }
        }
      }
    } catch (clipboardError) {
      console.log('📋 Clipboard API失败:', clipboardError.message)
    }
  }

  console.log('📋 未检测到可处理的图片格式')
}

// 检查文件是否为图片
const checkIfImageFile = (file) => {
  return new Promise((resolve) => {
    if (file.type && file.type.startsWith('image/')) {
      resolve(true)
      return
    }

    // 通过读取文件头判断
    const reader = new FileReader()
    reader.onload = (e) => {
      const arr = new Uint8Array(e.target.result)
      let header = ''
      for (let i = 0; i < Math.min(arr.length, 4); i++) {
        header += arr[i].toString(16).padStart(2, '0')
      }

      // 常见图片格式的文件头
      const imageHeaders = {
        '89504e47': 'png',
        'ffd8ffe0': 'jpg',
        'ffd8ffe1': 'jpg',
        'ffd8ffe2': 'jpg',
        'ffd8ffe3': 'jpg',
        'ffd8ffe8': 'jpg',
        '47494638': 'gif',
        '52494646': 'webp'
      }

      const isImage = Object.keys(imageHeaders).some(h => header.startsWith(h))
      console.log('📋 文件头检查:', header, '是否为图片:', isImage)
      resolve(isImage)
    }
    reader.onerror = () => resolve(false)
    reader.readAsArrayBuffer(file.slice(0, 4))
  })
}

// 处理图片文件
const processImageFile = async (file, source) => {
  console.log(`📋 处理${source}的图片:`, file.name || '未命名图片', file.type, file.size)

  // 生成文件名（如果没有名称）
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const extension = file.type ? file.type.split('/')[1] : 'png'
  const fileName = file.name || `粘贴图片-${timestamp}.${extension}`

  // 创建新的File对象，确保有正确的名称和类型
  const namedFile = new File([file], fileName, {
    type: file.type || 'image/png'
  })

  // 使用现有的上传处理逻辑
  emit('upload-change', { raw: namedFile })

  ElMessage.success(`检测到${source}图片，正在上传...`)
}

// 计算用户余额
const userBalance = computed(() => {
  const user = authStore.userInfo
  const balance = user?.current_balance

  // 处理各种可能的余额格式
  if (balance === null || balance === undefined || balance === '') {
    return 0
  }

  // 如果已经是数字类型
  if (typeof balance === 'number') {
    return isNaN(balance) ? 0 : balance
  }

  // 如果是字符串类型，尝试转换为数字
  if (typeof balance === 'string') {
    const numBalance = parseFloat(balance)
    return isNaN(numBalance) ? 0 : numBalance
  }

  // 其他情况，尝试转换
  const numBalance = Number(balance)
  return isNaN(numBalance) ? 0 : numBalance
})
</script>

<style scoped>
.input-area {
  padding: var(--spacing-md);
  background: var(--bg-color);
  border-top: 1px solid var(--border-light);
}

/* 粘贴图片时的视觉反馈 */
.input-area :deep(.el-textarea__inner) {
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.input-area :deep(.el-textarea__inner:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.pending-attachments {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}

.pending-thumb {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-base);
  background: var(--bg-color-light);
}

.pending-thumb img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: var(--transition-base);
}

.pending-thumb img:hover {
  transform: scale(1.05);
}

.pending-thumb .name {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-sm);
  gap: var(--spacing-sm);
}

.upload-progress {
  flex: 1;
  margin-right: var(--spacing-sm);
}

.balance-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.balance-label {
  font-weight: 500;
}

.balance-amount {
  font-weight: 600;
  color: var(--color-success);
  font-family: 'Courier New', monospace;
}

.balance-amount.balance-low {
  color: var(--color-warning);
}

.balance-amount.balance-negative {
  color: var(--color-danger);
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

@media (max-width: 768px) {
  .input-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .upload-progress {
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
  }

  .balance-info {
    justify-content: center;
    order: -1;
  }

  .action-buttons {
    justify-content: center;
  }
}
</style>
