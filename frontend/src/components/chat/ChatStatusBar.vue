<template>
  <div class="status-bar">
    <div class="status-info">
      <span>状态: {{ isGenerating ? '生成中' : '就绪' }}</span>
      <span>模型: {{ selectedModelName }}</span>

      <!-- PDF处理器状态 -->
      <span v-if="pdfStatus.show" class="pdf-status" :class="pdfStatus.class">
        <el-icon class="status-icon"><Document /></el-icon>
        {{ pdfStatus.text }}
      </span>

      <span v-if="lastUsageTokens">
        本次: {{ lastUsageTokens.prompt }}/{{ lastUsageTokens.completion }} tokens
      </span>
      <span v-if="lastCostUsd">费用: ${{ lastCostUsd }}</span>
      <span v-if="lastCostBreakdown" style="opacity:0.85;">{{ lastCostBreakdown }}</span>
    </div>
  </div>
</template>

<script setup>
import { Document } from '@element-plus/icons-vue'

defineProps({
  isGenerating: {
    type: Boolean,
    default: false
  },
  selectedModelName: {
    type: String,
    default: ''
  },
  lastUsageTokens: {
    type: Object,
    default: null
  },
  lastCostUsd: {
    type: String,
    default: ''
  },
  lastCostBreakdown: {
    type: String,
    default: ''
  },
  pdfStatus: {
    type: Object,
    default: () => ({ show: false, text: '', class: '' })
  }
})
</script>

<style scoped>
.status-bar {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-color-light);
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
}

.status-info {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  flex-wrap: wrap;
  align-items: center;
}

.pdf-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.pdf-status.loading {
  color: var(--color-primary);
  background: rgba(102, 126, 234, 0.1);
}

.pdf-status.ready {
  color: var(--color-success);
  background: rgba(103, 194, 58, 0.1);
}

.pdf-status.error {
  color: var(--color-danger);
  background: rgba(245, 108, 108, 0.1);
}

.status-icon {
  font-size: 14px;
}

.status-info span {
  white-space: nowrap;
}

@media (max-width: 768px) {
  .status-info {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}
</style>
