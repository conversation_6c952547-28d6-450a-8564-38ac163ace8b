<template>
  <div class="message-item" :class="messageClasses">
    <div class="message-avatar">
      <div class="avatar-circle" :class="avatarClasses">
        {{ avatarText }}
      </div>
    </div>

    <div class="message-content-wrapper">
      <div class="message-header">
        <span class="message-role">{{ roleText }}</span>
        <span v-if="message.role === 'assistant' && temperatureText" class="message-temperature">
          {{ temperatureText }}
        </span>
      </div>

      <div class="message-content">
        <div
          class="message-text"
          v-html="processedContent"
          @click="handleMessageClick"
        ></div>

        <!-- 消息底部信息 - 移到message-content内部 -->
        <div class="message-footer">
          <span class="message-time">{{ formattedTime }}</span>
          <span v-if="costText" class="message-cost">{{ costText }}</span>
          <div v-if="showActions" class="message-actions">
            <el-button
              :icon="DocumentCopy"
              size="small"
              type="primary"
              text
              class="copy-message-btn"
              @click="handleCopy"
              title="复制原始内容"
            />
            <el-button
              :icon="Delete"
              size="small"
              type="danger"
              text
              class="delete-message-btn"
              @click="handleDelete"
              title="删除消息"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Python代码执行弹窗 -->
  <CodeExecutionModal
    v-model="codeExecutionModalVisible"
    :code="currentExecutionCode"
  />
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { Delete, DocumentCopy } from '@element-plus/icons-vue'
import { formatTime } from '@/utils/messageUtils.js'
import { useMessageProcessing } from '@/composables/useMessageProcessing.js'
import CodeExecutionModal from './CodeExecutionModal.vue'

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  messageContents: {
    type: Map,
    default: () => new Map()
  },
  latexRenderEnabled: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['delete-message', 'open-image-viewer'])

// 响应式数据
const messageMetadata = ref(null)
const codeExecutionModalVisible = ref(false)
const currentExecutionCode = ref('')

// 计算属性
const messageClasses = computed(() => {
  return [
    'message-item',
    `message-${props.message.role}`,
    {
      'has-actions': props.showActions
    }
  ]
})

const avatarClasses = computed(() => {
  return [
    'avatar-circle',
    `avatar-${props.message.role}`
  ]
})

const avatarText = computed(() => {
  return props.message.role === 'user' ? '我' : 'AI'
})

const roleText = computed(() => {
  if (props.message.role === 'user') {
    return '我'
  } else {
    // 如果是AI消息，尝试使用从MinIO获取的role值（可能是模型名称）
    if (messageMetadata.value && messageMetadata.value.role && messageMetadata.value.role !== 'assistant') {
      return messageMetadata.value.role
    }
    return 'AI助手'
  }
})

const formattedTime = computed(() => {
  return formatTime(props.message.created_at || props.message.timestamp)
})

const temperatureText = computed(() => {
  if (props.message.role === 'assistant' && messageMetadata.value && messageMetadata.value.temperature !== undefined) {
    return `温度: ${messageMetadata.value.temperature}`
  }
  return null
})

const costText = computed(() => {
  if (messageMetadata.value && messageMetadata.value.cost) {
    const cost = messageMetadata.value.cost
    if (cost.total_cost !== undefined) {
      return `费用: $${cost.total_cost.toFixed(6)}`
    }
  }
  return null
})

// 使用消息处理工具
const { processMessage } = useMessageProcessing()

const processedContent = computed(() => {
  // 优先使用缓存的内容（已经处理过的）
  if (props.messageContents.has(props.message.id)) {
    return props.messageContents.get(props.message.id)
  }

  // 如果有content_url，返回加载提示
  if (props.message.content_url) {
    return '<div class="loading-content">正在加载消息内容...</div>'
  }

  // 否则处理原始内容
  const rawContent = props.message.content || ''
  if (!rawContent) return ''

  return processMessage(rawContent, {
    enableLatex: props.latexRenderEnabled,
    enableMarkdown: true
  })
})

// 方法
const handleDelete = () => {
  emit('delete-message', props.message.id)
}

// 复制原始消息内容
const handleCopy = async () => {
  try {
    let rawContent = ''

    // 方法1: 如果有content_url，直接从MinIO获取原始内容
    if (props.message.content_url) {
      try {
        const response = await fetch(props.message.content_url)
        if (response.ok) {
          const data = await response.json()
          if (data.content) {
            rawContent = data.content
          }
        }
      } catch (error) {
        console.warn('从content_url获取内容失败:', error)
      }
    }

    // 方法2: 如果方法1失败，尝试从message对象获取
    if (!rawContent && props.message.content) {
      rawContent = props.message.content
    }

    // 方法3: 如果前面都失败，尝试从messageContents获取（但这是渲染后的HTML）
    if (!rawContent && props.messageContents.has(props.message.id)) {
      // 这里获取到的是渲染后的HTML，我们需要提示用户
      ElMessage.warning('只能复制渲染后的内容，原始内容获取失败')
      rawContent = props.messageContents.get(props.message.id)
      // 尝试简单地移除HTML标签
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = rawContent
      rawContent = tempDiv.textContent || tempDiv.innerText || rawContent
    }

    if (!rawContent || !rawContent.trim()) {
      ElMessage.warning('没有可复制的内容')
      return
    }

    // 复制到剪贴板
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(rawContent)
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = rawContent
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'

      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      textArea.remove()
    }

    ElMessage.success('内容已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 处理消息内容中的点击事件（图片和代码按钮）
const handleMessageClick = (event) => {
  // 处理图片点击
  const img = event.target.closest('img')
  if (img) {
    const src = img.getAttribute('src')
    if (src) {
      // 如果是data URL（base64图片），直接使用
      if (src.startsWith('data:')) {
        emit('open-image-viewer', src)
        return
      }

      // 如果是其他URL，通过代理访问
      const proxiedUrl = src.startsWith('/api/v1/content/proxy?')
        ? src
        : `/api/v1/content/proxy?url=${encodeURIComponent(src)}`

      emit('open-image-viewer', proxiedUrl)
      return
    }
  }

  // 处理代码按钮点击
  handleCodeButtonClick(event)
}

// 代码复制功能
const copyCodeToClipboard = async (code) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 使用现代的Clipboard API
      await navigator.clipboard.writeText(code)
    } else {
      // 降级方案：使用传统的execCommand
      const textArea = document.createElement('textarea')
      textArea.value = code
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      textArea.remove()
    }
    ElMessage.success('代码已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 处理代码按钮点击（复制和运行）- 只处理当前组件内的按钮
const handleCodeButtonClick = (event) => {
  // 确保只处理当前消息组件内的按钮点击
  const messageElement = event.currentTarget.closest('.message-item')
  if (!messageElement) return

  // 处理Python运行按钮
  const pythonRunButton = event.target.closest('.code-run-btn.python')
  if (pythonRunButton && messageElement.contains(pythonRunButton)) {
    const encodedCode = pythonRunButton.getAttribute('data-code')
    if (encodedCode) {
      try {
        const code = decodeURIComponent(encodedCode)
        currentExecutionCode.value = code
        codeExecutionModalVisible.value = true
        console.log('🚀 打开Python代码执行模态框，代码长度:', code.length)
      } catch (err) {
        console.error('解码代码失败:', err)
        ElMessage.error('代码解析失败')
      }
    }
    return
  }

  // 处理HTML运行按钮
  const htmlRunButton = event.target.closest('.code-run-btn.html')
  if (htmlRunButton && messageElement.contains(htmlRunButton)) {
    const encodedCode = htmlRunButton.getAttribute('data-code')
    if (encodedCode) {
      try {
        const code = decodeURIComponent(encodedCode)
        runHtmlCode(code)
      } catch (err) {
        console.error('解码代码失败:', err)
        ElMessage.error('代码解析失败')
      }
    }
    return
  }

  // 处理Markdown转换按钮
  const convertButton = event.target.closest('.code-convert-btn')
  if (convertButton && messageElement.contains(convertButton)) {
    const encodedCode = convertButton.getAttribute('data-code')
    const isDocx = convertButton.classList.contains('docx')
    const isPpt = convertButton.classList.contains('ppt')

    if (encodedCode) {
      try {
        const code = decodeURIComponent(encodedCode)
        if (isDocx) {
          convertMarkdownToDocx(code)
        } else if (isPpt) {
          convertMarkdownToPpt(code)
        }
      } catch (err) {
        console.error('解码代码失败:', err)
        ElMessage.error('代码解析失败')
      }
    }
    return
  }

  // 处理复制按钮
  const copyButton = event.target.closest('.code-copy-btn')
  if (copyButton && messageElement.contains(copyButton)) {
    const encodedCode = copyButton.getAttribute('data-code')
    if (encodedCode) {
      try {
        const code = decodeURIComponent(encodedCode)
        copyCodeToClipboard(code)

        // 临时显示"已复制"反馈
        const copyText = copyButton.querySelector('.copy-text')
        if (copyText) {
          const originalText = copyText.textContent
          copyText.textContent = '已复制!'
          setTimeout(() => {
            copyText.textContent = originalText
          }, 2000)
        }
      } catch (err) {
        console.error('解码代码失败:', err)
        ElMessage.error('复制失败')
      }
    }
  }
}

// HTML代码运行功能
const runHtmlCode = (htmlCode) => {
  try {
    console.log('🌐 运行HTML代码，长度:', htmlCode.length)

    // 创建一个完整的HTML文档
    const fullHtmlCode = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML代码运行结果</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .header {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <strong>HTML代码运行结果</strong> - 由InspirFlow生成
    </div>
    ${htmlCode}
</body>
</html>
    `

    // 创建Blob对象
    const blob = new Blob([fullHtmlCode], { type: 'text/html' })
    const url = URL.createObjectURL(blob)

    // 在新窗口中打开
    const newWindow = window.open(url, '_blank')

    if (newWindow) {
      ElMessage.success('HTML代码已在新窗口中运行')

      // 5秒后清理URL对象
      setTimeout(() => {
        URL.revokeObjectURL(url)
      }, 5000)
    } else {
      ElMessage.error('无法打开新窗口，请检查浏览器弹窗设置')
    }

  } catch (error) {
    console.error('HTML代码运行失败:', error)
    ElMessage.error('HTML代码运行失败: ' + error.message)
  }
}

// 获取认证token
const getAuthToken = async () => {
  try {
    // 先尝试从localStorage获取token
    let token = localStorage.getItem('access_token')
    if (token) {
      return token
    }

    // 如果没有token，使用API密钥登录获取token
    const loginResponse = await fetch('/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        api_key: 'admin-api-key-change-in-production'
      })
    })

    if (loginResponse.ok) {
      const loginData = await loginResponse.json()
      token = loginData.access_token
      localStorage.setItem('access_token', token)
      return token
    } else {
      throw new Error('登录失败')
    }
  } catch (error) {
    console.error('获取认证token失败:', error)
    throw error
  }
}

// Markdown转换为DOCX
const convertMarkdownToDocx = async (markdownCode) => {
  try {
    console.log('📄 转换Markdown为DOCX，长度:', markdownCode.length)

    ElMessage.info('正在转换为DOCX文档...')

    const token = await getAuthToken()
    const response = await fetch('/api/v1/convert/markdown-to-docx', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        markdown: markdownCode,
        filename: `document_${Date.now()}.docx`
      })
    })

    if (!response.ok) {
      throw new Error(`转换失败: ${response.status} ${response.statusText}`)
    }

    // 获取文件名
    const contentDisposition = response.headers.get('Content-Disposition')
    let filename = 'document.docx'
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }

    // 下载文件
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('DOCX文档已生成并下载')

  } catch (error) {
    console.error('Markdown转DOCX失败:', error)
    ElMessage.error('转换失败: ' + error.message)
  }
}

// Markdown转换为PPT
const convertMarkdownToPpt = async (markdownCode) => {
  try {
    console.log('📊 转换Markdown为PPT，长度:', markdownCode.length)

    ElMessage.info('正在转换为PPT演示文稿...')

    const token = await getAuthToken()
    const response = await fetch('/api/v1/convert/markdown-to-ppt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        markdown: markdownCode,
        filename: `presentation_${Date.now()}.pptx`
      })
    })

    if (!response.ok) {
      throw new Error(`转换失败: ${response.status} ${response.statusText}`)
    }

    // 获取文件名
    const contentDisposition = response.headers.get('Content-Disposition')
    let filename = 'presentation.pptx'
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }

    // 下载文件
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    ElMessage.success('PPT演示文稿已生成并下载')

  } catch (error) {
    console.error('Markdown转PPT失败:', error)
    ElMessage.error('转换失败: ' + error.message)
  }
}

// 获取消息元数据
const fetchMessageMetadata = async () => {
  if (props.message.content_url) {
    try {
      const response = await fetch(props.message.content_url)
      if (response.ok) {
        const data = await response.json()
        messageMetadata.value = data
      }
    } catch (error) {
      console.warn('获取消息元数据失败:', error)
    }
  }
}

// 生命周期
onMounted(() => {
  fetchMessageMetadata()
})
</script>

<style scoped>
.message-item {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  position: relative;
}

.message-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}



.message-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.avatar-circle.avatar-user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.avatar-circle.avatar-assistant {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.message-content-wrapper {
  flex: 1;
  min-width: 0;
  position: relative;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.message-role {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.message-temperature {
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.message-content {
  position: relative;
}

.message-text {
  font-size: 18px;
  line-height: 1.7;
  color: #333;
  word-wrap: break-word;
  overflow-wrap: anywhere;
  word-break: break-word;
  max-width: 100%;
}

.message-text :deep(a) {
  color: #409eff;
  text-decoration: none;
}

.message-text :deep(a:hover) {
  text-decoration: underline;
}

.message-text :deep(br) {
  line-height: 1.8;
}

/* Markdown样式增强 */
.message-text :deep(h1) {
  font-size: 1.5em;
  font-weight: 600;
  margin: 16px 0 12px 0;
  color: #2c3e50;
  border-bottom: 2px solid #e1e4e8;
  padding-bottom: 8px;
}

.message-text :deep(h2) {
  font-size: 1.3em;
  font-weight: 600;
  margin: 14px 0 10px 0;
  color: #2c3e50;
  border-bottom: 1px solid #e1e4e8;
  padding-bottom: 6px;
}

.message-text :deep(h3) {
  font-size: 1.2em;
  font-weight: 600;
  margin: 12px 0 8px 0;
  color: #2c3e50;
}

.message-text :deep(h4),
.message-text :deep(h5),
.message-text :deep(h6) {
  font-size: 1.1em;
  font-weight: 600;
  margin: 10px 0 6px 0;
  color: #2c3e50;
}

.message-text :deep(p) {
  margin: 8px 0;
  line-height: 1.7;
}

.message-text :deep(ul),
.message-text :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
}

.message-text :deep(li) {
  margin: 4px 0;
  line-height: 1.6;
}

.message-text :deep(blockquote) {
  margin: 12px 0;
  padding: 8px 16px;
  border-left: 4px solid #409eff;
  background: #f8f9fa;
  color: #666;
  font-style: italic;
}

.message-text :deep(strong) {
  font-weight: 600;
  color: #2c3e50;
}

.message-text :deep(em) {
  font-style: italic;
  color: #555;
}

.message-text :deep(table) {
  border-collapse: collapse;
  margin: 12px 0;
  width: 100%;
  border: 1px solid #e1e4e8;
}

.message-text :deep(th),
.message-text :deep(td) {
  border: 1px solid #e1e4e8;
  padding: 8px 12px;
  text-align: left;
}

.message-text :deep(th) {
  background: #f6f8fa;
  font-weight: 600;
}

.message-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.message-time {
  font-size: 14px;
  color: #999;
}

.message-cost {
  font-size: 14px;
  color: #666;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.message-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.copy-message-btn,
.delete-message-btn {
  padding: 6px 8px !important;
  min-height: 32px !important;
  font-size: 16px !important;
}

.copy-message-btn:hover {
  background-color: var(--el-color-primary-light-9) !important;
  color: var(--el-color-primary) !important;
}

.delete-message-btn:hover {
  background-color: var(--el-color-danger-light-9) !important;
  color: var(--el-color-danger) !important;
}

/* 用户消息样式 */
.message-user {
  flex-direction: row-reverse;
}

.message-user .message-content-wrapper {
  text-align: right;
}

.message-user .message-header {
  justify-content: flex-end;
}

/* 消息内容容器 */
.message-content {
  display: inline-block;
  max-width: 80%;
}

.message-user .message-text {
  background: #f0f9ff;
  padding: 12px 16px;
  border-radius: 18px 18px 4px 18px;
  display: block;
  text-align: left;
}

.message-user .message-actions {
  left: 0;
  right: auto;
}

/* AI消息样式 */
.message-assistant .message-text {
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 18px 18px 18px 4px;
  border: 1px solid #e9ecef;
  display: block;
  text-align: left;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .message-item:not(:last-child) {
    border-bottom-color: #333;
  }

  .message-role {
    color: #e6e6e6;
  }

  .message-time {
    color: #999;
  }

  .message-text {
    color: #e6e6e6;
  }

  .message-user .message-text {
    background: #1a365d;
    border-color: #2d3748;
  }

  .message-assistant .message-text {
    background: #2d3748;
    border-color: #4a5568;
  }

  .message-actions {
    background: rgba(45, 55, 72, 0.9);

/* 限制代码块在消息气泡内的宽度，并允许横向滚动 */
.message-text :deep(.enhanced-code-block),
.message-text :deep(pre),
.message-text :deep(.code-block) {
  max-width: min(100%, 70vw);
  overflow: hidden;
}
.message-text :deep(.enhanced-code-block .code-content),
.message-text :deep(pre) {
  overflow-x: auto;
}
@media (max-width: 1024px) {
  .message-text :deep(.enhanced-code-block),
  .message-text :deep(pre),
  .message-text :deep(.code-block) {
    max-width: min(100%, 85vw);
  }
}
@media (max-width: 800px) {
  .message-text :deep(.enhanced-code-block),
  .message-text :deep(pre),
  .message-text :deep(.code-block) {
    max-width: 100%;
  }
}

  }
}

/* 代码块样式 */
.message-text :deep(.code-block) {
  margin: 8px 0;
  border-radius: 6px;
  overflow: hidden;
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
}

.message-text :deep(.code-block pre) {
  margin: 0;
  padding: 16px;
  background: transparent;
  overflow-x: auto;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.message-text :deep(.code-block code) {
  background: transparent;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
}

.message-text :deep(.inline-code) {
  background: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.9em;
  border: 1px solid #e1e4e8;
}

/* 数学公式样式 */
.message-text :deep(.math-block) {
  margin: 12px 0;
  text-align: center;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e1e4e8;
}

.message-text :deep(.math-inline) {
  display: inline-block;
  margin: 0 2px;
}

/* 图片样式 */
.message-text :deep(.message-image) {
  max-width: 100%;
  max-height: 400px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  cursor: pointer;
  display: block;
  margin: 8px 0;
  border: 1px solid #e1e4e8;
  transition: transform 0.2s ease;
}

.message-text :deep(.message-image:hover) {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 暗色主题下的代码和数学公式样式 */
.dark .message-text :deep(.code-block) {
  background: #161b22;
  border-color: #30363d;
}

.dark .message-text :deep(.inline-code) {
  background: #161b22;
  border-color: #30363d;
  color: #f0f6fc;
}

.dark .message-text :deep(.math-block) {
  background: #161b22;
  border-color: #30363d;
  color: #f0f6fc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-item {

    gap: 8px;
    padding: 12px 0;
  }

  .avatar-circle {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .message-user .message-text,
  .message-assistant .message-text {
    max-width: 90%;
    padding: 12px 16px;
    font-size: 16px;
  }
}
</style>
