<template>
  <div class="test-python-execution">
    <div class="container">
      <h1>Python代码执行测试</h1>
      
      <!-- JupyterLite状态 -->
      <div class="status-section">
        <h2>执行环境状态</h2>
        <JupyterLiteStatus 
          :status="loadingStatus" 
          :show-when-idle="true"
          @start-loading="startLoading"
        />
      </div>
      
      <!-- 测试代码示例 -->
      <div class="test-section">
        <h2>测试代码示例</h2>
        
        <div class="test-case">
          <h3>1. 基础Python代码</h3>
          <div class="enhanced-code-block">
            <div class="code-header">
              <span class="code-language">PYTHON</span>
              <div class="code-actions">
                <button class="code-run-btn" @click="runCode(basicCode)" title="运行Python代码">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="5,3 19,12 5,21"></polygon>
                  </svg>
                  <span class="run-text">运行</span>
                </button>
              </div>
            </div>
            <div class="code-content">
              <pre><code class="hljs language-python">{{ basicCode }}</code></pre>
            </div>
          </div>
        </div>
        
        <div class="test-case">
          <h3>2. 数学计算</h3>
          <div class="enhanced-code-block">
            <div class="code-header">
              <span class="code-language">PYTHON</span>
              <div class="code-actions">
                <button class="code-run-btn" @click="runCode(mathCode)" title="运行Python代码">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="5,3 19,12 5,21"></polygon>
                  </svg>
                  <span class="run-text">运行</span>
                </button>
              </div>
            </div>
            <div class="code-content">
              <pre><code class="hljs language-python">{{ mathCode }}</code></pre>
            </div>
          </div>
        </div>
        
        <div class="test-case">
          <h3>3. Matplotlib绘图</h3>
          <div class="enhanced-code-block">
            <div class="code-header">
              <span class="code-language">PYTHON</span>
              <div class="code-actions">
                <button class="code-run-btn" @click="runCode(plotCode)" title="运行Python代码">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="5,3 19,12 5,21"></polygon>
                  </svg>
                  <span class="run-text">运行</span>
                </button>
              </div>
            </div>
            <div class="code-content">
              <pre><code class="hljs language-python">{{ plotCode }}</code></pre>
            </div>
          </div>
        </div>
        
        <div class="test-case">
          <h3>4. 错误处理测试</h3>
          <div class="enhanced-code-block">
            <div class="code-header">
              <span class="code-language">PYTHON</span>
              <div class="code-actions">
                <button class="code-run-btn" @click="runCode(errorCode)" title="运行Python代码">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="5,3 19,12 5,21"></polygon>
                  </svg>
                  <span class="run-text">运行</span>
                </button>
              </div>
            </div>
            <div class="code-content">
              <pre><code class="hljs language-python">{{ errorCode }}</code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 代码执行弹窗 -->
    <CodeExecutionModal
      v-model="codeExecutionModalVisible"
      :code="currentExecutionCode"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useJupyterLite } from '@/composables/useJupyterLite'
import JupyterLiteStatus from '@/components/common/JupyterLiteStatus.vue'
import CodeExecutionModal from '@/components/chat/CodeExecutionModal.vue'

// JupyterLite Hook
const { startProgressiveLoading, getLoadingStatus } = useJupyterLite()

// 响应式数据
const codeExecutionModalVisible = ref(false)
const currentExecutionCode = ref('')

// 计算属性
const loadingStatus = computed(() => getLoadingStatus.value)

// 测试代码示例
const basicCode = `# 基础Python代码测试
print("Hello, Python!")
print("当前时间:", end=" ")

import datetime
now = datetime.datetime.now()
print(now.strftime("%Y-%m-%d %H:%M:%S"))

# 简单计算
result = 2 + 3 * 4
print(f"2 + 3 * 4 = {result}")

# 列表操作
numbers = [1, 2, 3, 4, 5]
squared = [x**2 for x in numbers]
print(f"平方数列表: {squared}")
`

const mathCode = `# 数学计算测试
import math
import numpy as np

print("=== 数学计算测试 ===")

# 基础数学函数
print(f"π = {math.pi:.6f}")
print(f"e = {math.e:.6f}")
print(f"sin(π/2) = {math.sin(math.pi/2):.6f}")

# NumPy数组操作
arr = np.array([1, 2, 3, 4, 5])
print(f"原数组: {arr}")
print(f"平均值: {np.mean(arr):.2f}")
print(f"标准差: {np.std(arr):.2f}")

# 矩阵运算
matrix = np.array([[1, 2], [3, 4]])
print(f"矩阵:\\n{matrix}")
print(f"矩阵行列式: {np.linalg.det(matrix):.2f}")
`

const plotCode = `# Matplotlib绘图测试
import matplotlib.pyplot as plt
import numpy as np

print("=== 生成图表 ===")

# 创建数据
x = np.linspace(0, 2*np.pi, 100)
y1 = np.sin(x)
y2 = np.cos(x)

# 创建图形
plt.figure(figsize=(10, 6))
plt.plot(x, y1, 'b-', label='sin(x)', linewidth=2)
plt.plot(x, y2, 'r--', label='cos(x)', linewidth=2)

plt.title('三角函数图像', fontsize=16)
plt.xlabel('x', fontsize=12)
plt.ylabel('y', fontsize=12)
plt.legend()
plt.grid(True, alpha=0.3)

print("图表已生成！")

# 第二个图：柱状图
plt.figure(figsize=(8, 6))
categories = ['A', 'B', 'C', 'D', 'E']
values = [23, 45, 56, 78, 32]

plt.bar(categories, values, color=['red', 'green', 'blue', 'orange', 'purple'])
plt.title('示例柱状图', fontsize=16)
plt.xlabel('类别', fontsize=12)
plt.ylabel('数值', fontsize=12)

print("柱状图已生成！")
`

const errorCode = `# 错误处理测试
print("=== 错误处理测试 ===")

try:
    print("正常输出：Hello World!")
    
    # 这里会产生一个错误
    result = 10 / 0
    print("这行不会执行")
    
except ZeroDivisionError as e:
    print(f"捕获到错误: {e}")
    
print("程序继续执行...")

# 故意产生一个未捕获的错误
undefined_variable
`

// 方法
const startLoading = () => {
  startProgressiveLoading(true)
}

const runCode = (code) => {
  currentExecutionCode.value = code
  codeExecutionModalVisible.value = true
}

// 生命周期
onMounted(() => {
  // 自动开始加载
  startLoading()
})
</script>

<style scoped>
.test-python-execution {
  min-height: 100vh;
  background: var(--bg-color-page);
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: var(--text-primary);
  margin-bottom: 30px;
  text-align: center;
}

h2 {
  color: var(--text-primary);
  margin: 30px 0 20px 0;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 10px;
}

h3 {
  color: var(--text-regular);
  margin: 20px 0 10px 0;
}

.status-section {
  margin-bottom: 40px;
}

.test-section {
  margin-bottom: 40px;
}

.test-case {
  margin-bottom: 30px;
}

/* 复用全局代码块样式 */
.enhanced-code-block {
  margin: 16px 0;
  border: 1px solid var(--border-light);
  border-radius: 8px;
  overflow: hidden;
  background: var(--bg-color);
  box-shadow: var(--box-shadow-base);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--bg-color-light);
  border-bottom: 1px solid var(--border-light);
}

.code-language {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
}

.code-content {
  position: relative;
}

.code-content pre {
  margin: 0;
  padding: 16px;
  background: transparent;
  border-radius: 0;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-primary);
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}
</style>
