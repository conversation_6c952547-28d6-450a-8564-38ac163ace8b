<template>
  <div class="chat-container">
    <!-- 侧边栏 -->
    <ChatSidebar
      :collapsed="sidebarCollapsed"
      :conversations="conversations"
      :current-conversation-id="currentConversationId"
      :models="models"
      :selected-model="selectedModel"
      @toggle-sidebar="toggleSidebar"
      @create-conversation="createNewConversation"
      @select-conversation="selectConversation"
      @delete-conversation="deleteConversation"
      @open-settings="openSystemSettings"
      @update:selected-model="selectedModel = $event"
    />

    <!-- 主聊天区域 -->
    <div class="main-content">
      <!-- 聊天消息区域 -->
      <ChatMessages
        ref="chatMessagesRef"
        :messages="messages"
        :is-generating="isGenerating"
        :message-contents="messageContents"
        :latex-render-enabled="latexRenderEnabled"
        @delete-message="deleteMessage"
      />

      <!-- 状态栏 -->
      <ChatStatusBar
        :is-generating="isGenerating"
        :selected-model-name="selectedModelName"
        :last-usage-tokens="lastUsageTokens"
        :last-cost-usd="lastCostUSD"
        :last-cost-breakdown="lastCostBreakdown"
      />

      <!-- 输入区域 -->
      <ChatInputArea
        v-model:input-message="inputMessage"
        :is-generating="isGenerating"
        :pending-previews="pendingPreviews"
        :uploading-image="uploadingImage"
        :upload-progress="uploadProgress"
        @send-message="sendMessage"
        @upload-change="onUploadChange"
        @open-image-viewer="openImageViewer"
        @remove-pending-attachment="removePendingAttachment"
        @upload-pdf="openPdfUpload"
      />
    </div>

    <!-- 图片预览查看器 -->
    <el-image-viewer
      v-if="imageViewerVisible"
      :url-list="imagePreviewList"
      :initial-index="imagePreviewIndex"
      @close="imageViewerVisible = false"
    />

    <!-- 系统设置面板 -->
    <SystemSettingsPanel
      :visible="systemSettingsVisible"
      :available-prompts="availablePrompts"
      :prompt-categories="promptCategories"
      :user-info="userInfo"
      v-model:temperature="temperature"
      v-model:latex-render-enabled="latexRenderEnabled"
      v-model:selected-prompts="selectedPrompts"
      v-model:custom-prompt="customPrompt"
      @close="closeSystemSettings"
      @save="saveSystemSettings"
      @reset="resetSystemSettings"
      @logout="logout"
    />

    <!-- 隐藏的PDF文件选择器 -->
    <input
      ref="pdfFileInput"
      type="file"
      accept="application/pdf"
      style="display: none"
      @change="handlePdfFileChange"
    />

    <!-- PDF控制按钮（当侧边栏关闭且有PDF时显示） -->
    <div
      v-if="!pdfViewer.sidebarVisible.value && pdfProcessor.currentPdf.value"
      class="pdf-control-button"
      @click="reopenPdfSidebar"
    >
      <el-button type="primary" size="small" circle>
        <el-icon><Document /></el-icon>
      </el-button>
      <span class="pdf-control-tooltip">打开PDF预览</span>
    </div>

    <!-- PDF侧边栏 -->
    <PdfSidebar
      v-if="pdfViewer.sidebarVisible.value"
      :visible="pdfViewer.sidebarVisible.value"
      :pdf-info="pdfProcessor.currentPdf.value"
      :pages="pdfProcessor.pages.value"
      :selected-pages="pdfProcessor.selectedPages.value"
      :is-loading="pdfProcessor.isLoading.value"
      :is-exporting="pdfViewer.isExporting.value"
      :export-progress="pdfViewer.exportProgress.value"
      :visible-range="pdfViewer.visibleRange.value"
      :pdf-list="pdfProcessor.pdfList.value"
      @close="closePdfSidebar"
      @update:visible="(value) => pdfViewer.sidebarVisible.value = value"
      @select-all="pdfProcessor.selectAll"
      @select-none="pdfProcessor.selectNone"
      @toggle-page="pdfProcessor.togglePage"
      @add-to-chat="handleAddPdfToChat"
      @scroll="onPdfSidebarScroll"
      @load-request="handlePdfLoadRequest"
      @ready="onPdfSidebarReady"
      @switch-pdf="handleSwitchPdf"
      @range-select="handleRangeSelect"
    />

  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import api, { get, post } from '@/api'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { Document } from '@element-plus/icons-vue'

// 导入组件
import ChatSidebar from '@/components/chat/ChatSidebar.vue'
import ChatMessages from '@/components/chat/ChatMessages.vue'
import ChatStatusBar from '@/components/chat/ChatStatusBar.vue'
import ChatInputArea from '@/components/chat/ChatInputArea.vue'
import SystemSettingsPanel from '@/components/chat/SystemSettingsPanel.vue'
import PdfUploadButton from '@/components/pdf/PdfUploadButton.vue'
import PdfSidebar from '@/components/pdf/PdfSidebar.vue'

// 导入组合式API
import { useChat } from '@/composables/useChat.js'
import { useImageUpload } from '@/composables/useImageUpload.js'
import { useMessageProcessing } from '@/composables/useMessageProcessing.js'
import { useJupyterLite } from '@/composables/useJupyterLite.js'
import { usePdfProcessor } from '@/composables/usePdfProcessor.js'
import { usePdfViewer } from '@/composables/usePdfViewer.js'

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

// 使用组合式API
const {
  conversations,
  models,
  messages,
  currentConversationId,
  selectedModel,
  temperature,
  inputMessage,
  isGenerating,
  messageContents,
  latexRenderEnabled,
  lastUsageTokens,
  lastCostUSD,
  lastCostBreakdown,
  selectedModelName,
  loadModels,
  loadConversations,
  selectConversation,
  createNewConversation,
  deleteConversation,
  deleteMessage,
  loadMessages
} = useChat()

const {
  uploadingImage,
  uploadProgress,
  pendingAttachments,
  pendingPreviews,
  imageViewerVisible,
  imagePreviewList,
  imagePreviewIndex,
  handleUploadChange,
  removePendingAttachment,
  openImageViewer,
  onMessageHtmlClick,
  clearPendingAttachments
} = useImageUpload()

const {
  processMessage,
  formatTime
} = useMessageProcessing()

// JupyterLite Hook
const { startProgressiveLoading, getLoadingStatus } = useJupyterLite()

// PDF处理Hook
const pdfProcessor = usePdfProcessor()
const pdfViewer = usePdfViewer()

// 响应式数据
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const userInfo = computed(() => authStore.userInfo)
const chatMessagesRef = ref()

// PDF相关状态
const pdfFileInput = ref(null)

// 系统设置相关
const systemSettingsVisible = ref(false)
const selectedPrompts = ref([])
const customPrompt = ref('')
const availablePrompts = ref([])
const promptCategories = ref([])

// 图片上传处理
const onUploadChange = async (uploadFile) => {
  const result = await handleUploadChange(uploadFile)
  if (result) {
    inputMessage.value = (inputMessage.value || '') + result
  }
}

// 系统设置相关方法
const openSystemSettings = () => {
  systemSettingsVisible.value = true
}

const closeSystemSettings = () => {
  systemSettingsVisible.value = false
}

const saveSystemSettings = () => {
  // 保存设置到localStorage
  const settings = {
    selectedModel: selectedModel.value,
    temperature: temperature.value,
    latexRenderEnabled: latexRenderEnabled.value,
    selectedPrompts: selectedPrompts.value,
    customPrompt: customPrompt.value
  }
  localStorage.setItem('systemSettings', JSON.stringify(settings))
  ElMessage.success('设置已保存')
  closeSystemSettings()
}

const resetSystemSettings = () => {
  selectedModel.value = 'gpt-3.5-turbo'
  temperature.value = 0.8
  latexRenderEnabled.value = true
  selectedPrompts.value = []
  customPrompt.value = ''
  ElMessage.success('设置已重置为默认值')
}

// PDF相关方法
const openPdfUpload = () => {
  if (!pdfProcessor.isReady.value) {
    // 允许先选择文件，初始化完成后将自动加载
    ElMessage.info('PDF处理器正在初始化，您可以先选择文件，加载会自动进行...')
  }

  // 触发文件选择器
  pdfFileInput.value?.click()
}

const handlePdfFileChange = async (event) => {
  const file = event.target.files?.[0]
  if (!file) return

  console.log('📄 PDF文件选择:', file.name)

  // 验证文件类型
  if (file.type !== 'application/pdf') {
    ElMessage.error('请选择PDF文件')
    return
  }

  // 验证文件大小（50MB限制）
  const maxSize = 50 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('PDF文件大小不能超过50MB')
    return
  }

  try {
    const success = await pdfProcessor.loadPdf(file)
    if (success) {
      // 确保传递正确的PDF信息
      const pdfInfo = {
        name: pdfProcessor.currentPdf.value?.name || file.name,
        totalPages: pdfProcessor.currentPdf.value?.totalPages || 0,
        size: pdfProcessor.currentPdf.value?.size || file.size
      }
      console.log('📄 PDF信息:', pdfInfo)
      await pdfViewer.openPdfSidebar(pdfInfo)

      // 延迟一点时间，确保侧栏DOM完全渲染后再触发缩略图渲染
      setTimeout(async () => {
        try {
          console.log('🖼️ 开始渲染首屏缩略图...', {
            visibleRange: pdfViewer.visibleRange.value,
            pagesData: pdfProcessor.pages.value?.slice(0, 3).map(p => ({
              num: p.num, rendered: p.rendered, loading: p.loading, hasUrl: !!p.thumbnailUrl
            }))
          })

          // 强制渲染前几页
          for (let i = 1; i <= Math.min(3, pdfInfo.totalPages); i++) {
            console.log(`🖼️ 强制渲染第${i}页`)
            await pdfProcessor.renderThumbnail(i)
          }

          console.log('🖼️ 首屏缩略图渲染完成')
        } catch (error) {
          console.warn('首屏缩略图渲染失败:', error)
        }
      }, 500)
    }
  } catch (error) {
    console.error('❌ PDF加载失败:', error)
    ElMessage.error('PDF文件加载失败: ' + error.message)
  }

  // 清空文件选择器
  event.target.value = ''
}

const closePdfSidebar = () => {
  console.log('🚪 关闭PDF侧边栏', {
    sidebarVisible: pdfViewer.sidebarVisible.value
  })
  // 仅关闭侧边栏，不主动清理PDF资源，以便跨会话保留
  pdfViewer.closePdfSidebar()
  console.log('🚪 侧边栏状态更新后:', pdfViewer.sidebarVisible.value)
  // 如需彻底清空PDF，可调用 pdfProcessor.cleanup()，提供单独“清空PDF”按钮更合理
}

const reopenPdfSidebar = async () => {
  console.log('🚪 重新打开PDF侧边栏', {
    currentPdf: pdfProcessor.currentPdf.value,
    sidebarVisible: pdfViewer.sidebarVisible.value
  })

  if (pdfProcessor.currentPdf.value) {
    await pdfViewer.openPdfSidebar(pdfProcessor.currentPdf.value)
    console.log('🚪 侧边栏状态更新后:', pdfViewer.sidebarVisible.value)
  } else {
    console.warn('⚠️ 没有当前PDF，无法重新打开侧边栏')
  }
}

const handleAddPdfToChat = async () => {
  if (pdfProcessor.selectedPages.value.size === 0) {
    ElMessage.warning('请先选择要添加的页面')
    return
  }

  pdfViewer.startExport()

  try {
    const files = await pdfProcessor.exportSelectedPages((current, total) => {
      pdfViewer.setExportProgress(current, total)
    })

    // 逐个上传导出的图片
    for (const file of files) {
      const result = await handleUploadChange({ raw: file })
      if (result) {
        inputMessage.value = (inputMessage.value || '') + result
      }
    }

    ElMessage.success(`成功添加${files.length}张图片到聊天`)
    pdfViewer.finishExport()

    // 可选：关闭侧边栏
    // closePdfSidebar()
  } catch (error) {
    console.error('❌ PDF导出失败:', error)
    ElMessage.error('PDF页面导出失败')
    pdfViewer.finishExport()
  }
}

const handlePdfLoadRequest = async (pageNum) => {
  console.log(`📤 收到缩略图加载请求: 第${pageNum}页`)
  try {
    const result = await pdfProcessor.renderThumbnail(pageNum)
    console.log(`📤 缩略图加载请求完成: 第${pageNum}页, 结果:`, !!result)
  } catch (error) {
    console.error(`📤 缩略图加载请求失败: 第${pageNum}页`, error)
  }
}

// 滚动事件：更新可见范围并懒渲染当前可见页的缩略图
const onPdfSidebarScroll = async () => {
  pdfViewer.handleScroll()
  await pdfProcessor.renderVisibleThumbnails(
    pdfViewer.visibleRange.value.start,
    pdfViewer.visibleRange.value.end
  )
}

// 加载可用提示词
// PdfSidebar 的滚动容器就绪时，绑定到 pdfViewer 并渲染首屏缩略图
const onPdfSidebarReady = async (el) => {
  try {
    pdfViewer.scrollContainer.value = el
    // 保持与 PdfSidebar.vue 中 itemHeight 一致
    pdfViewer.itemHeight.value = 240
    pdfViewer.updateVisibleRange()

    // 延迟渲染，确保PDF文档完全就绪
    setTimeout(async () => {
      try {
        console.log('🖼️ 侧栏就绪，开始渲染首屏缩略图...')
        await pdfProcessor.renderVisibleThumbnails(
          pdfViewer.visibleRange.value.start,
          pdfViewer.visibleRange.value.end
        )
      } catch (error) {
        console.warn('侧栏就绪后缩略图渲染失败:', error)
      }
    }, 300)
  } catch (e) {
    console.warn('初始化可见范围缩略图失败:', e)
  }
}

// 切换PDF文件
const handleSwitchPdf = async (pdfId) => {
  console.log('🔄 切换PDF文件:', pdfId)
  try {
    await pdfProcessor.switchToPdf(pdfId)
    // 重新渲染首屏缩略图
    setTimeout(async () => {
      await pdfProcessor.renderVisibleThumbnails(
        pdfViewer.visibleRange.value.start,
        pdfViewer.visibleRange.value.end
      )
    }, 200)
  } catch (error) {
    console.error('❌ 切换PDF失败:', error)
    ElMessage.error('切换PDF失败')
  }
}

// 页码范围选择
const handleRangeSelect = (startPage, endPage) => {
  console.log('📄 页码范围选择:', startPage, 'to', endPage)
  pdfProcessor.selectPageRange(startPage, endPage)
}

const loadAvailablePrompts = async () => {
  try {
    const response = await api.get('/api/v1/chat/prompts')
    console.log('🧩 提示词API响应:', response)
    if (response && response.success) {
      const { prompts = [], categories = [] } = (response && response.data) || response || {}
      availablePrompts.value = Array.isArray(prompts) ? prompts : []
      promptCategories.value = Array.isArray(categories) ? categories : []

      // 如果后端未返回categories，前端按prompt.category分组生成默认分类
      if ((!promptCategories.value || promptCategories.value.length === 0) && availablePrompts.value.length > 0) {
        const uniq = Array.from(new Set(availablePrompts.value.map(p => p.category || '其他')))
        promptCategories.value = uniq.map(id => ({ id, name: id, description: '' }))
      }
    } else {
      console.warn('提示词接口返回非成功响应:', response)
      availablePrompts.value = []
      promptCategories.value = []
    }
  } catch (error) {
    console.warn('加载提示词列表失败:', error)
  }
}

// 加载保存的设置
const loadSystemSettings = () => {
  const savedSettings = localStorage.getItem('systemSettings')
  if (savedSettings) {
    try {
      const settings = JSON.parse(savedSettings)
      if (settings.selectedModel) selectedModel.value = settings.selectedModel
      if (settings.temperature !== undefined) temperature.value = settings.temperature
      if (settings.latexRenderEnabled !== undefined) latexRenderEnabled.value = settings.latexRenderEnabled
      if (settings.selectedPrompts) selectedPrompts.value = settings.selectedPrompts
      if (settings.customPrompt) customPrompt.value = settings.customPrompt
    } catch (error) {
      console.warn('加载系统设置失败:', error)
    }
  }
}

// 切换侧边栏
const toggleSidebar = () => {
  appStore.toggleSidebar()
}



// 生命周期
onMounted(async () => {
  // 加载系统设置
  loadSystemSettings()

  // 加载可用提示词
  await loadAvailablePrompts()

  await loadInitialData()
})

// 监听LaTeX渲染设置变化并保存到localStorage
watch(latexRenderEnabled, (newValue) => {
  localStorage.setItem('latexRenderEnabled', newValue.toString())
  console.log('💾 LaTeX渲染设置已保存:', newValue)

  // 清除消息内容缓存，强制重新渲染所有消息
  messageContents.value.clear()
  console.log('🔄 已清除消息缓存，将重新渲染所有消息')

}, { immediate: false })



// 方法
const loadInitialData = async () => {
  try {
    appStore.setLoading(true, '加载数据中...')

    // 加载模型列表
    await loadModels()

    // 加载对话列表
    await loadConversations()

    // 如果有对话，选择第一个
    if (conversations.value.length > 0) {
      console.log('✅ 找到现有对话，选择第一个:', conversations.value[0])
      await selectConversation(conversations.value[0].id)
    } else {
      console.log('📝 没有现有对话，等待用户操作')
      // 确保当前对话ID为空
      currentConversationId.value = null
      messages.value = []
    }

    // 开始JupyterLite渐进式加载（后台进行）
    console.log('🚀 开始后台加载Python执行环境...')
    startProgressiveLoading(true).catch(error => {
      console.warn('JupyterLite加载失败，但不影响主要功能:', error)
    })

    // 开始PDF处理器后台初始化（延迟启动，避免影响主要功能）
    setTimeout(async () => {
      console.log('🔧 开始后台初始化PDF处理器...')

      try {
        // 右上角提示：开始加载
        ElNotification({
          title: 'PDF处理器',
          message: '正在后台加载PDF处理模块，稍后即可上传PDF文档',
          position: 'top-right',
          type: 'info',
          duration: 3000
        })

        await pdfProcessor.initializePdfJs()

        // 右上角提示：加载完成
        ElNotification({
          title: 'PDF处理器',
          message: '加载完成，现在可以上传PDF文档了',
          position: 'top-right',
          type: 'success',
          duration: 3000
        })
      } catch (error) {
        console.error('❌ PDF处理器初始化失败:', error)
        ElNotification({
          title: 'PDF处理器',
          message: '初始化失败，PDF功能暂不可用',
          position: 'top-right',
          type: 'error',
          duration: 5000
        })
      }
    }, 3000) // 延迟3秒启动

  } catch (error) {
    console.error('加载初始数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    appStore.setLoading(false)
  }
}



// 登出
const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要登出吗？', '确认登出', {
      type: 'warning'
    })

    authStore.logout()
    router.push('/login')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('登出失败:', error)
    }
  }
}

// 检查用户余额
const checkUserBalance = async () => {
  try {
    // 获取最新的用户信息
    await authStore.getCurrentUser()

    const currentUser = authStore.userInfo
    if (!currentUser) {
      ElMessage.error('无法获取用户信息，请重新登录')
      return false
    }

    // 安全地获取余额，确保是数字类型
    const rawBalance = currentUser.current_balance
    let balance = 0

    if (rawBalance !== null && rawBalance !== undefined && rawBalance !== '') {
      // 如果已经是数字类型
      if (typeof rawBalance === 'number') {
        balance = isNaN(rawBalance) ? 0 : rawBalance
      }
      // 如果是字符串类型，使用parseFloat转换
      else if (typeof rawBalance === 'string') {
        const numBalance = parseFloat(rawBalance)
        balance = isNaN(numBalance) ? 0 : numBalance
      }
      // 其他情况，尝试Number转换
      else {
        const numBalance = Number(rawBalance)
        balance = isNaN(numBalance) ? 0 : numBalance
      }
    }

    console.log('🔍 用户余额检查:', { rawBalance, balance, userInfo: currentUser })

    // 检查余额是否小于0
    if (balance < 0) {
      await ElMessageBox.alert(
        `您的账户余额不足（当前余额：$${balance.toFixed(6)}），无法发送消息。请充值后再试。`,
        '余额不足',
        {
          type: 'warning',
          confirmButtonText: '确定'
        }
      )
      return false
    }

    // 如果余额很低（小于0.01），给出警告但允许发送
    if (balance < 0.01 && balance >= 0) {
      try {
        await ElMessageBox.confirm(
          `您的账户余额较低（当前余额：$${balance.toFixed(6)}），建议及时充值。是否继续发送消息？`,
          '余额较低提醒',
          {
            type: 'warning',
            confirmButtonText: '继续发送',
            cancelButtonText: '取消'
          }
        )
      } catch (error) {
        if (error === 'cancel') {
          return false
        }
      }
    }

    return true
  } catch (error) {
    console.error('检查用户余额失败:', error)
    ElMessage.error('检查余额失败，请重试')
    return false
  }
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || isGenerating.value) return

  // 检查用户余额
  if (!await checkUserBalance()) {
    return
  }

  try {
    isGenerating.value = true
    const messageText = inputMessage.value.trim()

    // 确保有一个有效的对话
    let conversationId = currentConversationId.value

    // 如果没有选中对话，但有对话列表，优先选择第一个现有对话
    if (!conversationId && conversations.value.length > 0) {
      console.log('🔄 没有选中对话，选择第一个现有对话')
      await selectConversation(conversations.value[0].id)
      conversationId = currentConversationId.value
    }

    // 只有在完全没有对话的情况下才创建新对话
    if (!conversationId) {
      console.log('🆕 没有现有对话，创建新对话')
      // 创建新对话
      const createResponse = await post('/api/v1/conversations/', {
        title: messageText.length > 20 ? messageText.substring(0, 20) + '...' : messageText
      })

      if (createResponse.success && createResponse.conversation) {
        conversationId = createResponse.conversation.id
        conversations.value.unshift(createResponse.conversation)
        currentConversationId.value = conversationId
        messages.value = []
      } else {
        throw new Error('创建对话失败')
      }
    }

    // 检查是否为第一条消息（需要更新对话标题）
    const isFirstMessage = messages.value.length === 0

    const response = await post('/api/v1/chat/', {
      conversation_id: conversationId,
      message: messageText,
      model_name: selectedModel.value,
      temperature: temperature.value,
      attachments: pendingAttachments.value,
      selected_prompts: selectedPrompts.value,
      custom_prompt: customPrompt.value
    })

    if (response.success) {
      // 如果是第一条消息，自动更新对话标题
      if (isFirstMessage && currentConversationId.value) {
        try {
          // 生成对话标题（取前20个字符）
          const title = messageText.length > 20 ? messageText.substring(0, 20) + '...' : messageText

          const titleResponse = await post(`/api/v1/conversations/${currentConversationId.value}/title`, {
            title: title
          })

          if (titleResponse.success) {
            console.log('✅ 对话标题更新成功:', title)
            // 重新加载对话列表以更新标题显示
            await loadConversations()
          } else {
            console.warn('⚠️ 对话标题更新失败:', titleResponse.message)
          }
        } catch (titleError) {
          console.warn('⚠️ 更新对话标题时出错:', titleError)
        }
      }

      // 清空输入和附件
      inputMessage.value = ''
      clearPendingAttachments()

      // 更新消息列表
      await loadMessages(currentConversationId.value)

      // 更新计费信息
      if (response.usage) {
        lastUsageTokens.value = {
          prompt: response.usage.prompt_tokens,
          completion: response.usage.completion_tokens,
          total: response.usage.total_tokens
        }
      }

      if (response.cost) {
        lastCostUSD.value = response.cost.toFixed(6)
      }

      // 刷新用户余额（聊天完成后）
      try {
        await authStore.getCurrentUser()
      } catch (error) {
        console.warn('刷新用户余额失败:', error)
      }

      // 滚动到底部
      nextTick(() => {
        if (chatMessagesRef.value) {
          chatMessagesRef.value.scrollToBottom()
        }
      })
    } else {
      ElMessage.error(response.message || '发送消息失败')
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
  } finally {
    isGenerating.value = false
  }
}

// 获取安全的消息内容
const getSafeMessageContent = (message) => {
  if (!message) return ''

  // 优先使用缓存的内容
  if (messageContents.value.has(message.id)) {
    return messageContents.value.get(message.id)
  }

  // 如果有content_url，返回加载提示
  if (message.content_url) {
    return '<div class="loading-content">正在加载消息内容...</div>'
  }

  // 否则直接返回content
  return message.content || ''
}
</script>

<style scoped>
.chat-container {
  display: flex;
  height: 100vh;
  background: var(--bg-color-page);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.loading-content {
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-sm);
}

/* PDF控制按钮 */
.pdf-control-button {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.pdf-control-tooltip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s;
}

.pdf-control-button:hover .pdf-control-tooltip {
  opacity: 1;
}
</style>