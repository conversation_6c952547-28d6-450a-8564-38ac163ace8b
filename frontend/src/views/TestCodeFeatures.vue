<template>
  <div class="test-code-features">
    <div class="container">
      <h1>代码功能测试</h1>
      <p class="description">测试HTML运行和Markdown转换功能</p>
      
      <!-- HTML代码测试 -->
      <div class="test-section">
        <h2>HTML代码运行测试</h2>
        <p>点击运行按钮将在新窗口中打开HTML代码</p>
        
        <div class="test-case">
          <h3>1. 基础HTML页面</h3>
          <div class="enhanced-code-block">
            <div class="code-header">
              <span class="code-language">HTML</span>
              <div class="code-actions">
                <button class="code-run-btn html" @click="runHtmlCode(basicHtml)" title="在浏览器中运行HTML代码">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10,9 9,9 8,9"></polyline>
                  </svg>
                  <span class="run-text">运行</span>
                </button>
              </div>
            </div>
            <div class="code-content">
              <pre><code class="hljs language-html">{{ basicHtml }}</code></pre>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Markdown转换测试 -->
      <div class="test-section">
        <h2>Markdown转换测试</h2>
        <p>点击转换按钮将下载对应格式的文档</p>
        
        <div class="test-case">
          <h3>2. 文档示例</h3>
          <div class="enhanced-code-block">
            <div class="code-header">
              <span class="code-language">MARKDOWN</span>
              <div class="code-actions">
                <button class="code-convert-btn docx" @click="convertToDocx(documentMarkdown)" title="转换为DOCX文档">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                    <line x1="12" y1="18" x2="12" y2="12"></line>
                    <line x1="9" y1="15" x2="15" y2="15"></line>
                  </svg>
                  <span class="convert-text">DOCX</span>
                </button>
                <button class="code-convert-btn ppt" @click="convertToPpt(documentMarkdown)" title="转换为PPT演示文稿">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="4" width="18" height="12" rx="1" ry="1"></rect>
                    <line x1="7" y1="8" x2="17" y2="8"></line>
                    <line x1="7" y1="12" x2="17" y2="12"></line>
                    <line x1="7" y1="16" x2="13" y2="16"></line>
                  </svg>
                  <span class="convert-text">PPT</span>
                </button>
              </div>
            </div>
            <div class="code-content">
              <pre><code class="hljs language-markdown">{{ documentMarkdown }}</code></pre>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Pandoc状态 -->
      <div class="test-section">
        <h2>Pandoc状态</h2>
        <div class="status-card">
          <div v-if="pandocStatus.loading" class="status-loading">
            <el-icon class="rotating"><Loading /></el-icon>
            <span>检查Pandoc状态...</span>
          </div>
          <div v-else-if="pandocStatus.data" class="status-info">
            <div class="status-item">
              <span class="label">安装状态:</span>
              <span :class="pandocStatus.data.installed ? 'status-success' : 'status-error'">
                {{ pandocStatus.data.installed ? '✅ 已安装' : '❌ 未安装' }}
              </span>
            </div>
            <div v-if="pandocStatus.data.version" class="status-item">
              <span class="label">版本信息:</span>
              <span class="status-version">{{ pandocStatus.data.version }}</span>
            </div>
            <div class="status-item">
              <span class="label">支持格式:</span>
              <span class="status-formats">{{ pandocStatus.data.supported_formats.join(', ') }}</span>
            </div>
          </div>
          <div v-else class="status-error">
            <span>无法获取Pandoc状态</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'

// 响应式数据
const pandocStatus = ref({
  loading: true,
  data: null,
  error: null
})

// 测试代码示例
const basicHtml = '<h1>Hello World!</h1>\n<p>这是一个基础的HTML页面示例。</p>\n<div style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4); padding: 20px; border-radius: 10px; color: white; text-align: center;">\n  <h2>彩色渐变背景</h2>\n  <p>这个div有一个漂亮的渐变背景</p>\n</div>\n<ul>\n  <li>列表项 1</li>\n  <li>列表项 2</li>\n  <li>列表项 3</li>\n</ul>'

const documentMarkdown = '# 项目报告\n\n## 概述\n\n这是一个关于**InspirFlow**项目的详细报告。\n\n## 主要功能\n\n### 1. 聊天功能\n- 支持多轮对话\n- 实时消息传输\n- 消息历史记录\n\n### 2. 代码执行\n- Python代码执行\n- HTML代码运行\n- Markdown文档转换\n\n## 技术栈\n\n| 技术 | 用途 |\n|------|------|\n| Vue.js | 前端框架 |\n| FastAPI | 后端API |\n| Pyodide | Python执行环境 |\n| Pandoc | 文档转换 |\n\n## 代码示例\n\n```python\ndef hello_world():\n    print("Hello, World!")\n    return "Success"\n```\n\n## 总结\n\n项目进展顺利，所有核心功能已实现。\n\n> 这是一个引用块，用于强调重要信息。\n\n---\n\n**报告日期**: 2025年8月30日\n**作者**: InspirFlow团队'

// 方法
const runHtmlCode = (htmlCode) => {
  try {
    console.log('🌐 运行HTML代码，长度:', htmlCode.length)
    
    // 创建一个完整的HTML文档
    const fullHtmlCode = '<!DOCTYPE html>\n<html lang="zh-CN">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>HTML代码运行结果</title>\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;\n            margin: 20px;\n            line-height: 1.6;\n        }\n        .header {\n            background: #f8f9fa;\n            padding: 10px;\n            border-radius: 5px;\n            margin-bottom: 20px;\n            font-size: 14px;\n            color: #666;\n        }\n    </style>\n</head>\n<body>\n    <div class="header">\n        <strong>HTML代码运行结果</strong> - 由InspirFlow生成\n    </div>\n    ' + htmlCode + '\n</body>\n</html>'
    
    // 创建Blob对象
    const blob = new Blob([fullHtmlCode], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    
    // 在新窗口中打开
    const newWindow = window.open(url, '_blank')
    
    if (newWindow) {
      ElMessage.success('HTML代码已在新窗口中运行')
      
      // 5秒后清理URL对象
      setTimeout(() => {
        URL.revokeObjectURL(url)
      }, 5000)
    } else {
      ElMessage.error('无法打开新窗口，请检查浏览器弹窗设置')
    }
    
  } catch (error) {
    console.error('HTML代码运行失败:', error)
    ElMessage.error('HTML代码运行失败: ' + error.message)
  }
}

const convertToDocx = async (markdownCode) => {
  try {
    console.log('📄 转换Markdown为DOCX，长度:', markdownCode.length)

    ElMessage.info('正在转换为DOCX文档...')

    const token = await getAuthToken()
    const response = await fetch('http://localhost:20010/api/v1/convert/markdown-to-docx', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        markdown: markdownCode,
        filename: 'document_' + Date.now() + '.docx'
      })
    })
    
    if (!response.ok) {
      throw new Error('转换失败: ' + response.status + ' ' + response.statusText)
    }
    
    // 获取文件名
    const contentDisposition = response.headers.get('Content-Disposition')
    let filename = 'document.docx'
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }
    
    // 下载文件
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('DOCX文档已生成并下载')
    
  } catch (error) {
    console.error('Markdown转DOCX失败:', error)
    ElMessage.error('转换失败: ' + error.message)
  }
}

const convertToPpt = async (markdownCode) => {
  try {
    console.log('📊 转换Markdown为PPT，长度:', markdownCode.length)

    ElMessage.info('正在转换为PPT演示文稿...')

    const token = await getAuthToken()
    const response = await fetch('http://localhost:20010/api/v1/convert/markdown-to-ppt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        markdown: markdownCode,
        filename: 'presentation_' + Date.now() + '.pptx'
      })
    })
    
    if (!response.ok) {
      throw new Error('转换失败: ' + response.status + ' ' + response.statusText)
    }
    
    // 获取文件名
    const contentDisposition = response.headers.get('Content-Disposition')
    let filename = 'presentation.pptx'
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/['"]/g, '')
      }
    }
    
    // 下载文件
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    ElMessage.success('PPT演示文稿已生成并下载')
    
  } catch (error) {
    console.error('Markdown转PPT失败:', error)
    ElMessage.error('转换失败: ' + error.message)
  }
}

const getAuthToken = async () => {
  try {
    // 先尝试从localStorage获取token
    let token = localStorage.getItem('access_token')
    if (token) {
      return token
    }

    // 如果没有token，使用API密钥登录获取token
    const loginResponse = await fetch('http://localhost:20010/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        api_key: 'admin-api-key-change-in-production'
      })
    })

    if (loginResponse.ok) {
      const loginData = await loginResponse.json()
      token = loginData.access_token
      localStorage.setItem('access_token', token)
      return token
    } else {
      throw new Error('登录失败')
    }
  } catch (error) {
    console.error('获取认证token失败:', error)
    throw error
  }
}

const checkPandocStatus = async () => {
  try {
    pandocStatus.value.loading = true

    const token = await getAuthToken()
    const response = await fetch('http://localhost:20010/api/v1/convert/pandoc-status', {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    })

    if (response.ok) {
      pandocStatus.value.data = await response.json()
    } else {
      throw new Error('无法获取Pandoc状态')
    }
  } catch (error) {
    console.error('检查Pandoc状态失败:', error)
    pandocStatus.value.error = error.message
  } finally {
    pandocStatus.value.loading = false
  }
}

// 生命周期
onMounted(() => {
  checkPandocStatus()
})
</script>

<style scoped>
.test-code-features {
  min-height: 100vh;
  background: var(--bg-color-page);
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: var(--text-primary);
  margin-bottom: 10px;
  text-align: center;
}

.description {
  text-align: center;
  color: var(--text-secondary);
  margin-bottom: 40px;
}

h2 {
  color: var(--text-primary);
  margin: 40px 0 20px 0;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 10px;
}

h3 {
  color: var(--text-regular);
  margin: 20px 0 10px 0;
}

.test-section {
  margin-bottom: 40px;
}

.test-case {
  margin-bottom: 30px;
}

/* 复用全局代码块样式 */
.enhanced-code-block {
  margin: 16px 0;
  border: 1px solid var(--border-light);
  border-radius: 8px;
  overflow: hidden;
  background: var(--bg-color);
  box-shadow: var(--box-shadow-base);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--bg-color-light);
  border-bottom: 1px solid var(--border-light);
}

.code-language {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
}

.code-content {
  position: relative;
}

.code-content pre {
  margin: 0;
  padding: 16px;
  background: transparent;
  border-radius: 0;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-primary);
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}

.status-card {
  background: var(--bg-color);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--box-shadow-base);
}

.status-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
}

.rotating {
  animation: spin 1s linear infinite;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: var(--text-primary);
  min-width: 80px;
}

.status-success {
  color: var(--success-color);
  font-weight: 500;
}

.status-error {
  color: var(--danger-color);
  font-weight: 500;
}

.status-version {
  font-family: monospace;
  background: var(--bg-color-light);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.status-formats {
  color: var(--text-secondary);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
