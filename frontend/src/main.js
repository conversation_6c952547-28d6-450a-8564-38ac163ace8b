import { createApp } from 'vue'
import { createPinia } from 'pinia'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'

// 导入全局样式
import './styles/global.css'

// 导入代码高亮样式
import 'highlight.js/styles/github.css'

// 导入数学公式样式
import 'katex/dist/katex.min.css'

// 创建应用实例
const app = createApp(App)

// 注册 Pinia 状态管理
app.use(createPinia())

// 注册路由
app.use(router)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)
  
  // 这里可以添加错误上报逻辑
  // 例如发送到错误监控服务
}

// 全局属性
app.config.globalProperties.$APP_NAME = 'InspirFlow'
app.config.globalProperties.$APP_VERSION = '2.0.0'
app.config.devtools = true; // Vue 3中配置方式不同
// 挂载应用
app.mount('#app')

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 InspirFlow 前端应用启动成功')
  console.log('📦 Vue版本:', app.version)
  console.log('🌐 环境:', import.meta.env.MODE)
}
