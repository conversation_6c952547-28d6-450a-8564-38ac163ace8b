<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LaTeX渲染切换测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .controls {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .control-row {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }
        .test-content {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            background: white;
        }
        .math-block {
            text-align: center;
            margin: 16px 0;
        }
        .math-inline {
            display: inline;
        }
        .math-error {
            color: #f56c6c;
            background: #fef0f0;
            padding: 2px 4px;
            border-radius: 3px;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        button:hover {
            background: #f0f0f0;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <h1>LaTeX渲染切换功能测试</h1>
    
    <div class="controls">
        <div class="control-row">
            <label>LaTeX渲染:</label>
            <label class="switch">
                <input type="checkbox" id="latexToggle" checked>
                <span class="slider"></span>
            </label>
            <span id="status">开启</span>
        </div>
        <div class="control-row">
            <label>localStorage值:</label>
            <span id="storageValue">true</span>
            <button onclick="refreshStorage()">刷新</button>
        </div>
        <div class="control-row">
            <button onclick="testRender()">重新渲染</button>
        </div>
    </div>
    
    <div class="test-content">
        <h2>测试内容</h2>
        <div id="content">
            <p>这是一个行内公式：$E = mc^2$，还有另一个：$\sum_{i=1}^{n} x_i$</p>
            <p>这是一个块级公式：</p>
            <p>$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$</p>
            <p>更复杂的公式：$$\frac{d}{dx}\left( \int_{0}^{x} f(u) \, du\right) = f(x)$$</p>
        </div>
    </div>
    
    <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 8px;">
        <h3>测试说明</h3>
        <ol>
            <li>切换上方的LaTeX渲染开关</li>
            <li>点击"重新渲染"按钮</li>
            <li>观察数学公式是否正确切换渲染状态</li>
            <li>开启时显示渲染后的数学公式，关闭时显示原始LaTeX代码</li>
            <li>刷新页面检查设置是否被保存</li>
        </ol>
    </div>

    <script>
        const toggle = document.getElementById('latexToggle');
        const status = document.getElementById('status');
        const storageValue = document.getElementById('storageValue');
        const content = document.getElementById('content');
        
        // 原始内容
        const originalContent = `
            <p>这是一个行内公式：$E = mc^2$，还有另一个：$\\sum_{i=1}^{n} x_i$</p>
            <p>这是一个块级公式：</p>
            <p>$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$</p>
            <p>更复杂的公式：$$\\frac{d}{dx}\\left( \\int_{0}^{x} f(u) \\, du\\right) = f(x)$$</p>
        `;
        
        // 初始化
        function init() {
            const saved = localStorage.getItem('latexRenderEnabled');
            if (saved !== null) {
                toggle.checked = saved === 'true';
            }
            updateStatus();
            refreshStorage();
            testRender();
        }
        
        // 更新状态显示
        function updateStatus() {
            status.textContent = toggle.checked ? '开启' : '关闭';
        }
        
        // 刷新localStorage显示
        function refreshStorage() {
            storageValue.textContent = localStorage.getItem('latexRenderEnabled') || '未设置';
        }
        
        // 渲染内容
        function testRender() {
            content.innerHTML = originalContent;
            
            if (toggle.checked) {
                // 渲染LaTeX
                renderMath();
            }
            // 如果关闭，保持原始LaTeX代码不变
        }
        
        // 渲染数学公式
        function renderMath() {
            // 渲染块级公式
            content.innerHTML = content.innerHTML.replace(/\$\$([\s\S]+?)\$\$/g, (match, latex) => {
                try {
                    const rendered = katex.renderToString(latex.trim(), {
                        displayMode: true,
                        throwOnError: false
                    });
                    return `<div class="math-block">${rendered}</div>`;
                } catch (e) {
                    return `<div class="math-block math-error">$$${latex}$$</div>`;
                }
            });
            
            // 渲染行内公式
            content.innerHTML = content.innerHTML.replace(/\$([^$\n]+)\$/g, (match, latex) => {
                try {
                    const rendered = katex.renderToString(latex.trim(), {
                        displayMode: false,
                        throwOnError: false
                    });
                    return `<span class="math-inline">${rendered}</span>`;
                } catch (e) {
                    return `<span class="math-inline math-error">$${latex}$</span>`;
                }
            });
        }
        
        // 切换事件
        toggle.addEventListener('change', function() {
            localStorage.setItem('latexRenderEnabled', this.checked.toString());
            console.log('💾 LaTeX渲染设置已保存:', this.checked);
            updateStatus();
            refreshStorage();
            testRender();
        });
        
        // 页面加载时初始化
        init();
    </script>
</body>
</html>
