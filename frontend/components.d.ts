/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ChatInputArea: typeof import('./src/components/chat/ChatInputArea.vue')['default']
    ChatMessages: typeof import('./src/components/chat/ChatMessages.vue')['default']
    ChatSidebar: typeof import('./src/components/chat/ChatSidebar.vue')['default']
    ChatStatusBar: typeof import('./src/components/chat/ChatStatusBar.vue')['default']
    CodeExecutionModal: typeof import('./src/components/chat/CodeExecutionModal.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    JupyterLiteStatus: typeof import('./src/components/common/JupyterLiteStatus.vue')['default']
    MessageItem: typeof import('./src/components/chat/MessageItem.vue')['default']
    PdfPageThumbnail: typeof import('./src/components/pdf/PdfPageThumbnail.vue')['default']
    PdfSidebar: typeof import('./src/components/pdf/PdfSidebar.vue')['default']
    PdfUploadButton: typeof import('./src/components/pdf/PdfUploadButton.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SystemSettingsPanel: typeof import('./src/components/chat/SystemSettingsPanel.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
