<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌套代码围栏测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .input {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .output {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
        }
        .expected {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>嵌套代码围栏解析测试</h1>
    
    <div class="test-case">
        <h3>测试用例1：Markdown中嵌套Python</h3>
        <div class="input" id="input1">```markdown
这是一个包含代码的文档：
```python
def hello():
    print("Hello World")
```
文档结束
```</div>
        <div class="expected">
            <strong>期望结果：</strong>应该识别出一个markdown代码块，其中包含python代码作为内容
        </div>
        <div class="output" id="output1"></div>
    </div>

    <div class="test-case">
        <h3>测试用例2：HTML中嵌套JavaScript</h3>
        <div class="input" id="input2">```html
<div>
    <script>
```javascript
console.log("嵌套的JS代码");
function test() {
    return "Hello";
}
```
    </script>
</div>
```</div>
        <div class="expected">
            <strong>期望结果：</strong>应该识别出一个html代码块，其中包含javascript代码作为内容
        </div>
        <div class="output" id="output2"></div>
    </div>

    <div class="test-case">
        <h3>测试用例3：不同长度的围栏</h3>
        <div class="input" id="input3">````markdown
外层内容
```python
print("这里的三个反引号不应该结束外层的四个反引号块")
```
更多外层内容
````</div>
        <div class="expected">
            <strong>期望结果：</strong>应该识别出一个markdown代码块，包含python代码段
        </div>
        <div class="output" id="output3"></div>
    </div>

    <script>
        // 复制新的解析函数（从useMessageProcessing.js）
        const parseNestedCodeFences = (content) => {
            const codeFencePlaceholders = []
            const lines = content.split('\n')
            const result = []

            console.log('开始解析内容:', content)

            let i = 0
            while (i < lines.length) {
                const line = lines[i]
                const fenceMatch = line.match(/^(\s*)(```+)(\w*)(.*)$/)

                console.log(`第${i+1}行: "${line}"`, fenceMatch ? '匹配围栏' : '普通行')

                if (fenceMatch) {
                    const [, indent, fence, language] = fenceMatch
                    const fenceLength = fence.length

                    console.log(`开始围栏: 长度=${fenceLength}, 语言=${language}, 缩进="${indent}"`)

                    // 找到开始围栏，现在寻找匹配的结束围栏
                    const blockContent = []
                    let j = i + 1
                    let found = false

                    while (j < lines.length) {
                        const currentLine = lines[j]
                        const endFenceMatch = currentLine.match(/^(\s*)(```+)(.*)$/)

                        console.log(`  检查第${j+1}行: "${currentLine}"`)

                        if (endFenceMatch) {
                            const [, endIndent, endFence] = endFenceMatch
                            const endFenceLength = endFence.length

                            console.log(`  结束围栏候选: 长度=${endFenceLength}, 缩进="${endIndent}"`)

                            // 检查是否是匹配的结束围栏
                            if (endFenceLength >= fenceLength && endIndent === indent) {
                                // 找到匹配的结束围栏
                                console.log(`  ✓ 找到匹配的结束围栏`)
                                found = true
                                break
                            } else {
                                console.log(`  ✗ 不匹配，继续寻找`)
                            }
                        }

                        // 将行添加到块内容中
                        blockContent.push(currentLine)
                        j++
                    }

                    if (found) {
                        // 成功找到完整的代码块
                        const index = codeFencePlaceholders.length
                        const placeholder = `CODEFENCEPLACEHOLDER${index}CODEFENCEPLACEHOLDER`
                        codeFencePlaceholders.push({
                            placeholder,
                            language: language || '',
                            content: blockContent.join('\n'),
                            indent
                        })
                        result.push(placeholder)
                        console.log(`✓ 保存代码块: 语言=${language}, 内容行数=${blockContent.length}`)
                        i = j + 1 // 跳过结束围栏
                    } else {
                        // 没有找到结束围栏，作为普通行处理
                        console.log(`✗ 没有找到结束围栏，作为普通行处理`)
                        result.push(line)
                        i++
                    }
                } else {
                    // 普通行
                    result.push(line)
                    i++
                }
            }

            console.log('解析结果:', {
                content: result.join('\n'),
                placeholders: codeFencePlaceholders
            })

            return {
                content: result.join('\n'),
                placeholders: codeFencePlaceholders
            }
        }

        // 测试函数
        function runTest(inputId, outputId) {
            const input = document.getElementById(inputId).textContent
            const output = document.getElementById(outputId)
            
            console.log(`\n=== 测试 ${inputId} ===`)
            const result = parseNestedCodeFences(input)
            
            output.innerHTML = `
                <strong>解析结果：</strong><br>
                <strong>处理后内容：</strong><br>
                <pre style="background: #f0f0f0; padding: 10px; margin: 5px 0;">${result.content}</pre>
                <strong>代码块占位符 (${result.placeholders.length}个)：</strong><br>
                ${result.placeholders.map((p, i) => `
                    <div style="margin: 5px 0; padding: 5px; background: #fff; border: 1px solid #ccc;">
                        <strong>块 ${i+1}:</strong> 语言=${p.language || '无'}<br>
                        <strong>内容:</strong><br>
                        <pre style="background: #f9f9f9; padding: 5px; margin: 2px 0; font-size: 12px;">${p.content}</pre>
                    </div>
                `).join('')}
            `
        }

        // 运行所有测试
        window.onload = function() {
            runTest('input1', 'output1')
            runTest('input2', 'output2')
            runTest('input3', 'output3')
        }
    </script>
</body>
</html>
