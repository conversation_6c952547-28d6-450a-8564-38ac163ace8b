<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌套代码围栏修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .input-section, .output-section {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .code-display {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
        }
        .rendered-output {
            border: 2px solid #28a745;
            padding: 15px;
            border-radius: 5px;
            background: #f8fff8;
            margin-top: 10px;
        }
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 12px;
        }
        
        /* 增强代码块样式 */
        .enhanced-code-block {
            position: relative;
            margin: 16px 0;
            border-radius: 8px;
            background: #f8f9fa;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f1f3f4;
            border-bottom: 1px solid #e5e7eb;
            font-size: 12px;
        }

        .code-language {
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .code-content {
            position: relative;
        }

        .code-content pre {
            margin: 0;
            padding: 16px;
            background: transparent;
            border-radius: 0;
            overflow-x: auto;
            font-size: 14px;
            line-height: 1.6;
            color: #1f2937;
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
        }

        .code-content code {
            background: transparent !important;
            padding: 0 !important;
            border-radius: 0 !important;
            font-family: inherit;
            color: inherit;
        }
    </style>
</head>
<body>
    <h1>嵌套代码围栏修复验证测试</h1>
    
    <div class="test-case">
        <h2>测试用例1：Markdown中嵌套Python代码</h2>
        
        <div class="input-section">
            <h4>输入内容：</h4>
            <div class="code-display" id="input1">```markdown
这是一个包含代码的文档：
```python
def hello():
    print("Hello World")
```
文档结束
```</div>
        </div>
        
        <div class="output-section">
            <h4>处理结果：</h4>
            <div class="debug-info" id="debug1"></div>
            <div class="rendered-output" id="output1"></div>
        </div>
    </div>

    <div class="test-case">
        <h2>测试用例2：四个反引号嵌套三个反引号</h2>
        
        <div class="input-section">
            <h4>输入内容：</h4>
            <div class="code-display" id="input2">````markdown
外层markdown内容
```python
print("内层Python代码")
def test():
    return "嵌套测试"
```
外层内容结束
````</div>
        </div>
        
        <div class="output-section">
            <h4>处理结果：</h4>
            <div class="debug-info" id="debug2"></div>
            <div class="rendered-output" id="output2"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        // 复制修复后的解析函数
        const parseNestedCodeFences = (content) => {
            const codeFencePlaceholders = []
            const lines = content.split('\n')
            const result = []

            let i = 0
            while (i < lines.length) {
                const line = lines[i]
                const fenceMatch = line.match(/^(\s*)(```+)(\w*)(.*)$/)

                if (fenceMatch) {
                    const [, indent, fence, language] = fenceMatch
                    const fenceLength = fence.length

                    // 找到开始围栏，现在寻找匹配的结束围栏
                    const blockContent = []
                    let j = i + 1
                    let found = false

                    while (j < lines.length) {
                        const currentLine = lines[j]
                        const endFenceMatch = currentLine.match(/^(\s*)(```+)(.*)$/)

                        if (endFenceMatch) {
                            const [, endIndent, endFence] = endFenceMatch
                            const endFenceLength = endFence.length

                            // 检查是否是匹配的结束围栏
                            if (endFenceLength >= fenceLength && endIndent === indent) {
                                found = true
                                break
                            }
                        }

                        blockContent.push(currentLine)
                        j++
                    }

                    if (found) {
                        const index = codeFencePlaceholders.length
                        const placeholder = `CODEFENCEPLACEHOLDER${index}CODEFENCEPLACEHOLDER`
                        codeFencePlaceholders.push({
                            placeholder,
                            language: language || '',
                            content: blockContent.join('\n'),
                            indent
                        })
                        result.push(placeholder)
                        i = j + 1
                    } else {
                        result.push(line)
                        i++
                    }
                } else {
                    result.push(line)
                    i++
                }
            }

            return {
                content: result.join('\n'),
                placeholders: codeFencePlaceholders
            }
        }

        // 模拟完整的处理流程
        const processMarkdownContent = (content) => {
            // 步骤1：保护嵌套代码围栏
            const { content: protectedContent, placeholders: codeFencePlaceholders } = parseNestedCodeFences(content)
            
            // 步骤2：marked处理
            let processed = marked.parse(protectedContent)
            
            // 步骤3：恢复代码围栏占位符
            codeFencePlaceholders.forEach((item) => {
                // 检查占位符是否被marked修改了
                const originalPlaceholder = item.placeholder
                let actualPlaceholder = originalPlaceholder
                
                // marked可能会将占位符包装在<p>标签中
                if (!processed.includes(originalPlaceholder)) {
                    const wrappedPlaceholder = `<p>${originalPlaceholder}</p>`
                    if (processed.includes(wrappedPlaceholder)) {
                        actualPlaceholder = wrappedPlaceholder
                    }
                }
                
                // HTML转义代码内容
                const escapedContent = item.content
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#39;')

                // 创建增强的代码块结构
                const languageClass = item.language ? ` class="language-${item.language}"` : ''
                const codeBlock = `<div class="enhanced-code-block">
                    <div class="code-header">
                        ${item.language ? `<span class="code-language">${item.language.toUpperCase()}</span>` : ''}
                    </div>
                    <div class="code-content">
                        <pre><code${languageClass}>${escapedContent}</code></pre>
                    </div>
                </div>`

                processed = processed.split(actualPlaceholder).join(codeBlock)
            })
            
            return {
                processed,
                debug: {
                    protectedContent,
                    codeFencePlaceholders,
                    markedOutput: marked.parse(protectedContent)
                }
            }
        }

        // 运行测试
        function runTest(inputId, outputId, debugId) {
            const input = document.getElementById(inputId).textContent
            const result = processMarkdownContent(input)
            
            // 显示调试信息
            document.getElementById(debugId).innerHTML = `
                <strong>调试信息：</strong><br>
                <strong>1. 保护后内容：</strong><br>
                <div class="code-display">${result.debug.protectedContent}</div>
                <strong>2. Marked输出：</strong><br>
                <div class="code-display">${result.debug.markedOutput}</div>
                <strong>3. 代码块占位符 (${result.debug.codeFencePlaceholders.length}个)：</strong><br>
                ${result.debug.codeFencePlaceholders.map((p, i) => `
                    <div style="margin: 5px 0; padding: 5px; background: #e9ecef; border: 1px solid #adb5bd; border-radius: 3px;">
                        <strong>块 ${i+1}:</strong> 语言=${p.language || '无'}, 占位符=${p.placeholder}<br>
                        <strong>内容:</strong><br>
                        <div class="code-display" style="max-height: 100px;">${p.content}</div>
                    </div>
                `).join('')}
            `
            
            // 显示最终结果
            document.getElementById(outputId).innerHTML = result.processed
        }

        // 页面加载后运行测试
        window.onload = function() {
            if (typeof marked !== 'undefined') {
                runTest('input1', 'output1', 'debug1')
                runTest('input2', 'output2', 'debug2')
            } else {
                setTimeout(() => {
                    runTest('input1', 'output1', 'debug1')
                    runTest('input2', 'output2', 'debug2')
                }, 1000)
            }
        }
    </script>
</body>
</html>
